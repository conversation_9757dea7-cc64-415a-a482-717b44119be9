import os
import time
import sys  # 添加sys模块以支持命令行参数
import re  # 添加re模块以支持正则表达式
from datetime import datetime
from supabase import create_client, Client  # 导入Supabase客户端

# 配置Supabase连接
SUPABASE_URL = 'https://wjanjmsywbydjbfrdkaz.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE'
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

FILE_MODIFICATION_IDLE_THRESHOLD = 60  # 文件修改空闲阈值，秒（1分钟）

# 日志函数
def log(message, level="INFO"):
    """带时间戳的日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def log_info(message):
    """信息级别日志"""
    log(message, "INFO")

def log_warning(message):
    """警告级别日志"""
    log(message, "WARN")

def log_error(message):
    """错误级别日志"""
    log(message, "ERROR")

def log_success(message):
    """成功级别日志"""
    log(message, "SUCCESS")

# 从 record.js 文件读取 PROGRAM_TAG
def get_program_tag():
    """从 record.js 文件读取 PROGRAM_TAG 的值"""
    try:
        record_js_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'record.js')
        if not os.path.exists(record_js_path):
            log_error(f"record.js 文件不存在: {record_js_path}")
            return None
            
        with open(record_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正则表达式提取 PROGRAM_TAG 的值
        match = re.search(r"const\s+PROGRAM_TAG\s*=\s*['\"]([^'\"]+)['\"]", content)
        if match:
            program_tag = match.group(1)
            log_success(f"成功读取 PROGRAM_TAG: {program_tag}")
            return program_tag
        else:
            log_error("无法在 record.js 中找到 PROGRAM_TAG 定义")
            return None
            
    except Exception as e:
        log_error(f"读取 PROGRAM_TAG 时出错: {str(e)}")
        return None

# 获取本机的 PROGRAM_TAG
PROGRAM_TAG = get_program_tag()
if not PROGRAM_TAG:
    log_warning("无法读取 PROGRAM_TAG，将不会设置 tag 字段")
    PROGRAM_TAG = None

def get_human_readable_age(seconds_diff):
    """
    将秒数差转换为易读的时间字符串，如 '2 天 3 小时 15 分钟前'。
    """
    if seconds_diff < 0:
        seconds_diff = 0 # 处理可能的时钟同步问题或未来时间戳

    days = int(seconds_diff // (24 * 3600))
    seconds_diff %= (24 * 3600)
    hours = int(seconds_diff // 3600)
    seconds_diff %= 3600
    minutes = int(seconds_diff // 60)
    seconds = int(seconds_diff % 60)

    parts = []
    if days > 0:
        parts.append(f"{days} 天")
    if hours > 0:
        parts.append(f"{hours} 小时")
    if minutes > 0:
        parts.append(f"{minutes} 分钟")
    
    # 即使天、小时、分钟都为0，也显示秒，除非所有部分都为空（例如0秒）
    if not parts or (days == 0 and hours == 0 and minutes == 0):
        parts.append(f"{seconds} 秒")
        
    if not parts: # 应该是0秒的情况
        return "刚刚"
    return " ".join(parts) + "前"

# 将Unix时间戳转换为ISO格式字符串，添加自douyin.js
def timestamp_to_iso(timestamp):
    return time.strftime("%Y-%m-%dT%H:%M:%S.000Z", time.gmtime(timestamp))

def create_file_id_record(anchor_id, file_path):
    """
    在file_id表中创建记录，与record.js保持一致
    
    Args:
        anchor_id (str): 主播ID
        file_path (str): 要添加的文件绝对路径
    
    Returns:
        bool: 创建成功返回True，失败返回False
    """
    try:
        # 确保是绝对路径
        absolute_path = os.path.abspath(file_path)
        file_name = os.path.basename(file_path)
        
        # 从文件名中提取timestamp作为recordtime
        # 文件名格式：{timestamp}-{anchorId}.ts 或 {timestamp}-{anchorId}.mp4
        timestamp_match = re.match(r'^(\d+)-', file_name)
        if not timestamp_match:
            log_error(f"无法从文件名提取时间戳: {file_name}")
            return False
            
        # 将Unix时间戳转换为UTC ISO格式
        timestamp = int(timestamp_match.group(1))
        record_time = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(timestamp))
        
        # 检查是否已存在相同的记录
        check_response = supabase.table('file_id').select('*').match({
            'anchor_id': anchor_id,
            'recordtime': record_time
        }).execute()
        
        # 使用标志变量控制是否需要插入新记录
        need_insert = True
        
        if check_response.data and len(check_response.data) > 0:
            existing_record = check_response.data[0]
            existing_status = existing_record.get('status')
            existing_file_id = existing_record.get('file_id')
            existing_acount = existing_record.get('acount', 0) or 0
            
            log_info(f"file_id表中已存在记录: anchor_id={anchor_id}, recordtime={record_time}, status={existing_status}, file_id={existing_file_id}")
            
            # 如果status为空、null或pending，或者file_id为空，删除记录并重新写入
            if (existing_status is None or existing_status == '' or existing_status == 'pending' or 
                existing_file_id is None or existing_file_id == ''):
                log_info(f"status为空/null/pending或file_id为空，删除现有记录并重新写入")
                
                # 删除现有记录
                delete_response = supabase.table('file_id').delete().match({
                    'anchor_id': anchor_id,
                    'recordtime': record_time
                }).execute()
                
                if delete_response.data:
                    log_success(f"成功删除现有记录")
                    # 删除成功后，需要插入新记录
                    need_insert = True
                else:
                    log_error(f"删除现有记录失败")
                    return False
            else:
                # status不为空/null/pending且file_id有值，删除本地文件并更新记录
                log_info(f"文件已被处理 (status: {existing_status}, file_id: {existing_file_id})，准备删除本地文件: {file_path}")
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        log_success(f"成功删除已处理的文件: {file_path}")
                    else:
                        log_info(f"文件不存在，无需删除: {file_path}")
                except Exception as e:
                    log_error(f"删除文件时发生错误: {e}")
                
                # 更新warning和acount字段
                warning_message = f"find程序发送过本地文件，但是因为记录已存在，本地文件已删除"
                existing_warning = existing_record.get('warning', '')
                if existing_warning:
                    new_warning = existing_warning + '\n' + warning_message
                else:
                    new_warning = warning_message
                    
                update_response = supabase.table('file_id').update({
                    'warning': new_warning,
                    'acount': existing_acount + 1
                }).match({
                    'anchor_id': anchor_id,
                    'recordtime': record_time
                }).execute()
                
                if update_response.data:
                    log_success(f"成功更新warning和acount字段")
                else:
                    log_error(f"更新warning和acount字段失败")
                    
                return True
        
        # 只有在需要插入新记录时才执行以下代码
        if not need_insert:
            return False
        
        # 查询anchors表获取title, platform和ordertime
        anchor_response = supabase.table('anchors').select('title,platform,ordertime,tag').eq('anchor_id', anchor_id).single().execute()
        
        # 准备插入数据
        file_id_data = {
            'anchor_id': anchor_id,
            'recordtime': record_time,
            # 不设置 file_id，让数据库使用默认值 NULL
            'file_path': absolute_path,
            'status': 'pending'    # 状态字段
            # mp4 和 ts 字段由数据库自动管理，不应手动设置
        }
        
        # 添加额外字段
        if anchor_response.data:
            if anchor_response.data.get('title'):
                file_id_data['title'] = anchor_response.data['title']
            if anchor_response.data.get('platform'):
                file_id_data['platform'] = anchor_response.data['platform']
            if anchor_response.data.get('ordertime'):
                file_id_data['betime'] = anchor_response.data['ordertime']
        
        # 设置tag字段为本程序的标识（与record.js保持一致）
        if PROGRAM_TAG:
            file_id_data['tag'] = PROGRAM_TAG
                
            # 同时更新tag字段
            current_tag = anchor_response.data.get('tag', '')
            if PROGRAM_TAG:
                update_data = {}
                if not current_tag:
                    # tag为空，直接设置为PROGRAM_TAG
                    update_data['tag'] = PROGRAM_TAG
                else:
                    # 检查tag中是否已包含PROGRAM_TAG
                    tags = [t.strip() for t in current_tag.split('/') if t.strip()]
                    if PROGRAM_TAG not in tags:
                        # 如果不包含，则添加
                        update_data['tag'] = current_tag + '/' + PROGRAM_TAG
                
                if update_data:
                    # 更新anchors表的tag字段
                    supabase.table('anchors').update(update_data).eq('anchor_id', anchor_id).execute()
                    log_info(f"更新tag字段: {update_data['tag']}")
        
        # 插入记录
        insert_response = supabase.table('file_id').insert(file_id_data).execute()
        
        if insert_response.data:
            log_success(f"成功在file_id表中创建记录: anchor_id={anchor_id}, file_path={absolute_path}, recordtime={record_time} (timestamp={timestamp})")
            return True
        else:
            log_error(f"在file_id表中创建记录失败: anchor_id={anchor_id}")
            return False
            
    except Exception as e:
        log_error(f"在file_id表中创建记录时发生错误: {e}")
        return False

def execute_copy_command():
    """直接执行 copy 命令，不扫描文件"""
    source_file = 'file9.py'
    if os.path.exists(source_file):
        log_info(f"\n准备复制 {source_file} 到 file1.py - file16.py")
        
        # 读取源文件内容
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            copied_count = 0
            for i in range(1, 17):  # 1 到 16
                target_file = f'file{i}.py'
                try:
                    with open(target_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    copied_count += 1
                    log_success(f"已复制到: {target_file}")
                except Exception as e:
                    log_error(f"复制到 {target_file} 时发生错误: {e}")
            
            log_success(f"\n复制完成！共成功复制到 {copied_count} 个文件。")
        except Exception as e:
            log_error(f"读取源文件 {source_file} 时发生错误: {e}")
    else:
        log_error(f"源文件 {source_file} 不存在！")

def find_and_process_files(directory, auto_action=None):
    """
    扫描指定目录及其子目录中的所有文件，显示详细信息，
    并分别询问是否发送所有文件。

    Args:
        directory (str): 要扫描的根目录。
        auto_action (str): 自动执行的操作，可选值：'yes', 'large', 'del', 'no'
    """
    current_time = time.time()
    all_files_details = [] # 存储所有文件的详细信息 (path, size_str, age_str)
    all_files_to_process = []  # 修改：将存储所有文件，不再区分文件类型
    # 添加文件大小信息的字典
    file_sizes = {}  # 存储文件路径和字节大小的映射

    log_info(f"开始扫描目录: {directory}")
    log_info(f"将扫描并列出所有文件，并显示其大小和最后修改时间距今多久。")
    log_info("-" * 80)

    file_count = 0
    for root, _, files in os.walk(directory):
        for filename in files:
            file_count += 1
            file_path = os.path.join(root, filename)
            try:
                file_mtime = os.path.getmtime(file_path)
                file_age_seconds = current_time - file_mtime
                file_size_bytes = os.path.getsize(file_path)
                file_sizes[file_path] = file_size_bytes  # 存储文件大小

                if file_size_bytes < 1024:
                    file_size_str = f"{file_size_bytes} B"
                elif file_size_bytes < 1024 * 1024:
                    file_size_str = f"{file_size_bytes / 1024:.2f} KB"
                else:
                    file_size_str = f"{file_size_bytes / (1024 * 1024):.2f} MB"
                
                age_str = get_human_readable_age(file_age_seconds)
                detail = (file_path, file_size_str, age_str)
                all_files_details.append(detail)
                log_info(f"发现文件: {file_path} (大小: {file_size_str}, 修改于: {age_str})")

                # 修改：只要文件修改时间超过1分钟，就添加到处理列表，不再区分文件类型
                if file_age_seconds >= FILE_MODIFICATION_IDLE_THRESHOLD:
                    all_files_to_process.append(file_path)
                else:
                    log_warning(f"跳过文件: {file_path} (修改时间未超过1分钟)")
            
            except FileNotFoundError:
                log_warning(f"文件在扫描时未找到 (可能已被删除): {file_path}")
            except Exception as e:
                log_error(f"处理文件 {file_path} 时发生错误: {e}")

    log_info("-" * 80)
    log_success(f"扫描完成。共扫描到 {file_count} 个文件。")

    # 定义文件大小常量
    TWO_GB = 2 * 1024 * 1024 * 1024
    ONE_POINT_FIVE_GB = int(1.5 * 1024 * 1024 * 1024)

    # 处理所有符合条件的视频文件
    sent_count = 0
    if all_files_to_process:
        # 只筛选出视频文件（mp4和ts）
        video_files = [f for f in all_files_to_process if f.endswith('.ts') or f.endswith('.mp4')]
        
        # 将视频文件分为大于2G和小于等于2G两组
        large_files = [f for f in video_files if file_sizes.get(f, 0) > TWO_GB]
        small_files = [f for f in video_files if file_sizes.get(f, 0) <= TWO_GB]
        
        log_info(f"\n检测到 {len(all_files_to_process)} 个文件，其中 {len(video_files)} 个是视频文件（mp4/ts）:")
        if large_files:
            log_info(f"\n大于2G的视频文件 ({len(large_files)} 个):")
            for f_path in large_files:
                size_gb = file_sizes.get(f_path, 0) / (1024 * 1024 * 1024)
                log_info(f"  - {f_path} ({size_gb:.2f} GB)")
        
        if small_files:
            log_info(f"\n小于等于2G的视频文件 ({len(small_files)} 个):")
            for f_path in small_files:
                size_bytes = file_sizes.get(f_path, 0)
                if size_bytes < 1024 * 1024:
                    size_str = f"{size_bytes / 1024:.2f} KB"
                elif size_bytes < 1024 * 1024 * 1024:
                    size_str = f"{size_bytes / (1024 * 1024):.2f} MB"
                else:
                    size_str = f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
                log_info(f"  - {f_path} ({size_str})")
        
        log_info(f"\n总计: {len(video_files)} 个视频文件待处理")
        
        # 如果提供了自动操作参数，使用它；否则询问用户
        if auto_action:
            user_input = auto_action
            log_info(f"自动执行操作: {user_input}")
        else:
            user_input = input("请选择要发送的文件 (large=仅2G以上, yes=仅2G以下, del=删除2G以上, close=删除1.5-2G, oth=删除非ts和mp4文件, clear=删除所有文件, copy=复制file9到file1-16, no=不发送): ").strip().lower()
        
        files_to_send = []
        if user_input == 'large':
            files_to_send = large_files
            log_info(f"将发送 {len(large_files)} 个大于2G的视频文件。")
        elif user_input == 'yes':
            files_to_send = small_files
            log_info(f"将发送 {len(small_files)} 个小于等于2G的视频文件。")
        elif user_input == 'del':
            # 删除大于2G的文件
            deleted_count = 0
            for f_path in large_files:
                try:
                    os.remove(f_path)
                    deleted_count += 1
                    log_success(f"已删除文件: {f_path}")
                except Exception as e:
                    log_error(f"删除文件 {f_path} 时发生错误: {e}")
            log_success(f"共删除了 {deleted_count} 个大于2G的文件。")
        elif user_input == 'oth':
            # 删除非ts和mp4格式的文件
            deleted_count = 0
            other_files = []
            for f_path in all_files_to_process:
                if not (f_path.endswith('.ts') or f_path.endswith('.mp4')):
                    other_files.append(f_path)
            
            if other_files:
                log_info(f"\n发现 {len(other_files)} 个非ts和mp4格式的文件:")
                for f_path in other_files:
                    print(f"  - {f_path}")
                
                confirm = input("\n确定要删除这些文件吗? (y/n): ").strip().lower()
                if confirm == 'y':
                    for f_path in other_files:
                        try:
                            os.remove(f_path)
                            deleted_count += 1
                            log_success(f"已删除文件: {f_path}")
                        except Exception as e:
                            log_error(f"删除文件 {f_path} 时发生错误: {e}")
                    log_success(f"共删除了 {deleted_count} 个非ts和mp4格式的文件。")
                else:
                    log_info("取消删除操作。")
            else:
                log_info("未发现任何非ts和mp4格式的文件。")
        elif user_input == 'close':
            # 删除1.5G-2G之间的文件
            deleted_count = 0
            files_in_range = []
            for f_path in all_files_to_process:
                file_size = file_sizes.get(f_path, 0)
                if ONE_POINT_FIVE_GB <= file_size <= TWO_GB:
                    files_in_range.append(f_path)
            
            if files_in_range:
                log_info(f"\n发现 {len(files_in_range)} 个在1.5G-2G之间的文件:")
                for f_path in files_in_range:
                    size_gb = file_sizes.get(f_path, 0) / (1024 * 1024 * 1024)
                    log_info(f"  - {f_path} ({size_gb:.2f} GB)")
                
                confirm = input("\n确定要删除这些文件吗? (y/n): ").strip().lower()
                if confirm == 'y':
                    for f_path in files_in_range:
                        try:
                            os.remove(f_path)
                            deleted_count += 1
                            log_success(f"已删除文件: {f_path}")
                        except Exception as e:
                            log_error(f"删除文件 {f_path} 时发生错误: {e}")
                    log_success(f"共删除了 {deleted_count} 个在1.5G-2G之间的文件。")
                else:
                    log_info("取消删除操作。")
            else:
                log_info("未发现任何在1.5G-2G之间的文件。")
        elif user_input == 'clear':
            # 删除所有扫描到的文件
            deleted_count = 0
            if all_files_to_process:
                log_warning(f"\n将要删除 {len(all_files_to_process)} 个文件:")
                for f_path in all_files_to_process:
                    size_bytes = file_sizes.get(f_path, 0)
                    if size_bytes < 1024 * 1024:
                        size_str = f"{size_bytes / 1024:.2f} KB"
                    elif size_bytes < 1024 * 1024 * 1024:
                        size_str = f"{size_bytes / (1024 * 1024):.2f} MB"
                    else:
                        size_str = f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
                    log_info(f"  - {f_path} ({size_str})")
                
                confirm = input("\n确定要删除所有这些文件吗? (y/n): ").strip().lower()
                if confirm == 'y':
                    for f_path in all_files_to_process:
                        try:
                            os.remove(f_path)
                            deleted_count += 1
                            log_success(f"已删除文件: {f_path}")
                        except Exception as e:
                            log_error(f"删除文件 {f_path} 时发生错误: {e}")
                    log_success(f"共删除了 {deleted_count} 个文件。")
                else:
                    log_info("取消删除操作。")
            else:
                log_info("没有需要删除的文件。")
        elif user_input == 'copy':
            # 调用 copy 命令函数
            execute_copy_command()
        elif user_input == 'no':
            log_info("未发送任何文件。")
        else:
            log_warning("无效的选择，未发送任何文件。")
        
        if files_to_send:
            for f_path in files_to_send:
                try:
                    # 从文件路径中提取anchor_id (假设文件路径格式为 'recordings/anchor_id/...' 或 './recordings/anchor_id/...')
                    path_parts = f_path.split(os.sep)
                    
                    # 查找 'recordings' 所在位置并获取下一个部分作为 anchor_id
                    anchor_id = None
                    for i, part in enumerate(path_parts):
                        if part == 'recordings' and i + 1 < len(path_parts):
                            anchor_id = path_parts[i + 1]
                            break
                    
                    if anchor_id:
                        file_mtime = os.path.getmtime(f_path)
                        recordtime = timestamp_to_iso(file_mtime)
                        
                        # 对于.temp.ts文件，修改路径为.ts
                        file_path_to_send = f_path
                        if f_path.endswith('.temp.ts'):
                            file_path_to_send = f_path[:-8] + '.ts'  # 将.temp.ts替换为.ts
                            log_info(f"注意: 将文件路径从 {f_path} 修改为 {file_path_to_send}")
                        
                        # 在file_id表中创建记录
                        if create_file_id_record(anchor_id, file_path_to_send):
                            sent_count += 1
                            log_success(f"文件 {f_path} 已在file_id表中创建记录。")
                        else:
                            log_warning(f"文件 {f_path} 在file_id表中创建记录失败。")
                    else:
                        log_warning(f"无法从路径中提取anchor_id: {f_path}")
                except FileNotFoundError:
                    log_warning(f"文件 {f_path} 在尝试发送时未找到。")
                except Exception as e:
                    log_error(f"发送文件 {f_path} 时发生错误: {e}")
            if sent_count > 0:
                log_success(f"共成功发送了 {sent_count} 个文件。")
    else:
        log_info("\n未发现任何符合条件的文件。")

if __name__ == "__main__":
    RECORDINGS_DIR = './recordings'
    
    # 检查命令行参数
    auto_action = None
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['yes', 'large', 'del', 'close', 'oth', 'clear', 'copy', 'no']:
            auto_action = arg
        else:
            log_error(f"无效的参数 '{arg}'。有效参数: yes, large, del, close, oth, clear, copy, no")
            sys.exit(1)

    # 如果是 copy 命令，直接执行，不需要扫描目录
    if auto_action == 'copy':
        execute_copy_command()
    else:
        # 其他命令需要扫描目录
        if not os.path.isdir(RECORDINGS_DIR):
            log_error(f"目录 '{RECORDINGS_DIR}' 不存在。请确保该目录存在或路径正确。")
        else:
            find_and_process_files(RECORDINGS_DIR, auto_action) 