const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { URL } = require('url');
const { exec } = require('child_process');
const util = require('util');
const os = require('os');

// 内存中的处理锁管理
const processingLocks = new Map();

// 将exec转换为Promise版本
const execPromise = util.promisify(exec);

// 日志级别定义
const LOG_LEVEL = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

// 当前使用的日志级别，可在配置文件中修改
const CURRENT_LOG_LEVEL = LOG_LEVEL.INFO;

/**
 * 日志工具函数，根据级别过滤输出
 * @param {number} level 日志级别
 * @param {string} message 日志消息
 * @param {string} anchorId 可选，关联主播ID
 */
function log(level, message, anchorId = '') {
  if (level >= CURRENT_LOG_LEVEL) {
    const anchorInfo = anchorId ? `[anchor_id: ${anchorId}] ` : '';
    console.log(`[${getCurrentTime()}] ${anchorInfo}${message}`);
  }
}

/**
 * 错误日志工具函数
 * @param {string} message 错误消息
 * @param {Error} error 错误对象
 * @param {string} anchorId 可选，关联主播ID
 */
function logError(message, error, anchorId = '') {
  if (LOG_LEVEL.ERROR >= CURRENT_LOG_LEVEL) {
    const anchorInfo = anchorId ? `[anchor_id: ${anchorId}] ` : '';
    console.error(`[${getCurrentTime()}] ${anchorInfo}${message}: ${error.message}`);
  }
}

/**
 * API调用错误处理包装函数，支持重试
 * @param {Function} operation 异步操作函数
 * @param {number} maxRetries 最大重试次数
 * @param {string} operationName 操作名称，用于日志
 * @param {string} anchorId 可选，关联主播ID
 * @returns {Promise<any>} 操作结果
 */
async function withRetry(operation, maxRetries = 3, operationName = 'API调用', anchorId = '') {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (err) {
      // 某些错误永远不会成功，不需要重试
      if (attempt === maxRetries - 1 || 
          err.message.includes('Permission') || 
          err.message.includes('not found') ||
          err.message.includes('权限不足')) {
        throw err;
      }
        
      log(LOG_LEVEL.WARN, `${operationName}失败，${attempt+1}/${maxRetries}次尝试, 等待重试...`, anchorId);
      await new Promise(r => setTimeout(r, 1000 * (attempt + 1)));
    }
  }
}

/**
 * 安全文件操作包装函数
 * @param {Function} operation 文件操作函数
 * @param {string} errorMessage 错误消息
 * @param {string} anchorId 可选，关联主播ID
 * @returns {Promise<any>} 操作结果
 */
async function safeFileOperation(operation, errorMessage, anchorId = '') {
  try {
    return await operation();
  } catch (err) {
    if (err.code !== 'ENOENT') {
      logError(errorMessage, err, anchorId);
    }
    return null;
  }
}

// PROGRAM_TAG 用于在 does 表中查找对应字段获取区间
const PROGRAM_TAG = 'jp2';

// 配置部分
const url = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE';

const supabase = createClient(url, key);

/**
 * 向主播的 log 字段追加日志信息
 * @param {string} anchorId 主播ID
 * @param {string} message 日志消息
 */
async function appendAnchorLog(anchorId, message) {
  try {
    const timestamp = getCurrentTime();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    // 追加模式：先获取现有日志，然后追加
    const { data, error: fetchError } = await supabase
      .from('anchors')
      .select('log')
      .eq('anchor_id', anchorId)
      .single();
    
    if (fetchError) {
      logError(`获取主播日志失败`, fetchError, anchorId);
      return;
    }
    
    const existingLog = data?.log || '';
    const newLog = existingLog + logEntry;
    
    // 限制日志长度，保留最新的50000个字符（约50KB）
    const trimmedLog = newLog.length > 50000 ? newLog.slice(-50000) : newLog;
    
    const { error: updateError } = await supabase
      .from('anchors')
      .update({ log: trimmedLog })
      .eq('anchor_id', anchorId);
    
    if (updateError) {
      logError(`更新主播日志失败`, updateError, anchorId);
    }
  } catch (err) {
    logError(`记录主播日志时发生异常`, err, anchorId);
  }
}

/**
 * 在 file_id 表中创建失败状态的文件记录
 * @param {string} anchorId 主播ID
 * @param {string} filePath 文件路径
 * @param {string} recordTime 录制时间
 * @param {string} errorMessage 错误信息
 * @param {object} additionalData 额外数据（title, platform, ordertime等）
 */
async function createFailedFileIdRecord(anchorId, filePath, recordTime, errorMessage, additionalData = {}) {
  try {
    // 将Unix时间戳转换为ISO格式
    const recordTimeISO = new Date(parseInt(recordTime) * 1000).toISOString();
    
    // 准备插入数据
    const fileName = path.basename(filePath);
    const fileIdData = {
      'anchor_id': anchorId,
      'recordtime': recordTimeISO,
      'file_path': filePath,
      'status': 'failed',    // 标记为失败状态
      'log': errorMessage    // 记录错误信息
    };
    
    // 添加额外字段
    if (additionalData.title) {
      fileIdData['title'] = additionalData.title;
    }
    if (additionalData.platform) {
      fileIdData['platform'] = additionalData.platform;
    }
    if (additionalData.ordertime) {
      fileIdData['betime'] = additionalData.ordertime;
    }
    fileIdData['tag'] = PROGRAM_TAG;
    
    // 插入记录
    const { error: insertError } = await supabase
      .from('file_id')
      .insert(fileIdData);
    
    if (insertError) {
      logError(`在file_id表中创建失败记录时出错`, insertError, anchorId);
      await appendAnchorLog(anchorId, `创建失败记录时出错: ${insertError.message}, 文件: ${path.basename(filePath)}`);
    } else {
      log(LOG_LEVEL.INFO, `成功在file_id表中创建失败记录: anchor_id=${anchorId}, file_path=${filePath}, status=failed, log=${errorMessage}`, anchorId);
      await appendAnchorLog(anchorId, `成功创建失败记录: ${path.basename(filePath)}, 原因: ${errorMessage}`);
    }
  } catch (err) {
    logError(`创建失败记录时发生错误`, err, anchorId);
  }
}

/**
 * 在file_id表中创建或更新记录
 * @param {string} anchorId 主播ID
 * @param {string} filePath 文件路径
 * @param {string} recordTime 录制时间
 * @param {object} additionalData 额外数据（title, platform, ordertime等）
 */
async function createFileIdRecord(anchorId, filePath, recordTime, additionalData = {}) {
  try {
    // 将Unix时间戳转换为ISO格式
    const recordTimeISO = new Date(parseInt(recordTime) * 1000).toISOString();
    
    // 检查是否已存在相同的记录
    const { data: existingRecords, error: checkError } = await supabase
      .from('file_id')
      .select('*')
      .match({
        'anchor_id': anchorId,
        'recordtime': recordTimeISO
      });
    
    if (checkError) {
      logError(`检查file_id表现有记录时出错`, checkError, anchorId);
      await appendAnchorLog(anchorId, `检查file_id表时出错: ${checkError.message}`);
      return;
    }
    
    if (existingRecords && existingRecords.length > 0) {
      log(LOG_LEVEL.INFO, `file_id表中已存在记录: anchor_id=${anchorId}, recordtime=${recordTimeISO} (timestamp=${recordTime})`, anchorId);
      
      // 更新bcount字段
      const existingRecord = existingRecords[0];
      const existingBcount = existingRecord.bcount || 0;
      
      const { error: updateError } = await supabase
        .from('file_id')
        .update({
          'bcount': existingBcount + 1
        })
        .match({
          'anchor_id': anchorId,
          'recordtime': recordTimeISO
        });
        
      if (updateError) {
        logError(`更新bcount字段时出错`, updateError, anchorId);
      } else {
        log(LOG_LEVEL.INFO, `成功更新bcount字段: ${existingBcount} -> ${existingBcount + 1}`, anchorId);
      }
      
      return;
    }
    
    // 准备插入数据
    const fileName = path.basename(filePath);
    const fileIdData = {
      'anchor_id': anchorId,
      'recordtime': recordTimeISO,  // 使用转换后的ISO格式时间
      // 不设置 file_id，让数据库使用默认值
      'file_path': filePath,
      'status': 'pending'    // 状态字段
      // mp4 和 ts 字段保持默认值，由后续处理程序设置
    };
    
    // 添加额外字段
    if (additionalData.title) {
      fileIdData['title'] = additionalData.title;
    }
    if (additionalData.platform) {
      fileIdData['platform'] = additionalData.platform;
    }
    if (additionalData.ordertime) {
      fileIdData['betime'] = additionalData.ordertime;
    }
    // file_id表的tag字段应该记录本程序的标识
    fileIdData['tag'] = PROGRAM_TAG;
    
    // 插入记录
    const { error: insertError } = await supabase
      .from('file_id')
      .insert(fileIdData);
    
    if (insertError) {
      logError(`在file_id表中创建记录时出错`, insertError, anchorId);
      await appendAnchorLog(anchorId, `创建file_id记录失败: ${insertError.message}, 文件: ${path.basename(filePath)}`);
    } else {
      log(LOG_LEVEL.INFO, `成功在file_id表中创建记录: anchor_id=${anchorId}, file_path=${filePath}, recordtime=${recordTimeISO} (timestamp=${recordTime})`, anchorId);
      await appendAnchorLog(anchorId, `成功创建file_id记录: ${path.basename(filePath)}, 时间戳: ${recordTime}`);
    }
  } catch (err) {
    logError(`创建file_id记录时发生异常`, err, anchorId);
  }
}

// 使用 file_id 表记录

const RECORDING_DIR = path.resolve('./recordings');

// 全局记录所有活跃的录制任务
// 每个 key: anchor_id, value: { ffmpegProcess, recordingPromise, anchorInfo: { anchor_name, platform }, latestOutputFile, isStopping }
const activeRecordings = new Map();

// 新增 Map cleanupDone 记录各录制是否完整执行尾流程
// 已废弃：现在使用基于文件的锁机制来防止并发处理
// const cleanupDone = new Map();

// 用于记录每个主播最后使用的URL，避免重复使用相同的无效链接
const lastUsedUrls = new Map();

// 用于跟踪status=false但is_recording=true的主播次数

// 用于存储当前区间的下限和上限（offset 与 limit）
let currentOffset = 0;
let currentLimit = 0; // limit = end - start

/**
 * 获取当前时间字符串
 */
const getCurrentTime = () => {
  return new Date().toLocaleString();
};

/**
 * 检查磁盘空间
 * @returns {Promise<number>} 返回可用空间（GB）
 */
async function checkDiskSpace() {
  try {
    const { stdout } = await execPromise('df -BG / | tail -1');
    const parts = stdout.trim().split(/\s+/);
    const available = parseInt(parts[3].replace('G', ''));
    return available;
  } catch (err) {
    logError('检查磁盘空间失败', err);
    return 100; // 发生错误时返回一个安全值，避免影响录制
  }
}

/**
 * 全局磁盘空间不足标志
 */
let isDiskSpaceLow = false;

/**
 * 检查磁盘空间并更新状态
 */
async function monitorDiskSpace() {
  const availableGB = await checkDiskSpace();
  const wasLow = isDiskSpaceLow;
  isDiskSpaceLow = availableGB < 10;
  
  if (isDiskSpaceLow && !wasLow) {
    log(LOG_LEVEL.WARN, `磁盘空间不足！当前可用: ${availableGB}GB < 10GB，执行清理脚本`);
    
    // 执行 find.py 清理文件
    log(LOG_LEVEL.INFO, `执行 python3 find.py yes 清理文件以释放空间`);
    try {
      const { stdout, stderr } = await execPromise('python3 find.py yes', { 
        cwd: process.cwd()
      });
      if (stdout) {
        log(LOG_LEVEL.INFO, `find.py 执行成功: ${stdout.trim()}`);
      }
      if (stderr) {
        log(LOG_LEVEL.WARN, `find.py 执行警告: ${stderr.trim()}`);
      }
      
      // 清理后重新检查磁盘空间
      const newAvailableGB = await checkDiskSpace();
      if (newAvailableGB >= 10) {
        isDiskSpaceLow = false;
        log(LOG_LEVEL.INFO, `清理后磁盘空间已恢复: ${newAvailableGB}GB >= 10GB`);
      } else {
        log(LOG_LEVEL.WARN, `清理后磁盘空间仍然不足: ${newAvailableGB}GB < 10GB`);
      }
    } catch (err) {
      logError('执行 find.py 失败', err);
    }
  } else if (!isDiskSpaceLow && wasLow) {
    log(LOG_LEVEL.INFO, `磁盘空间恢复正常！当前可用: ${availableGB}GB >= 10GB`);
  }
  
  // 记录当前磁盘空间状态
  log(LOG_LEVEL.INFO, `当前磁盘可用空间: ${availableGB}GB${isDiskSpaceLow ? ' (空间不足!)' : ''}`);
}

/**
 * 停止所有录制任务
 */
async function stopAllRecordings() {
  log(LOG_LEVEL.WARN, `开始停止所有录制任务，当前活跃录制数: ${activeRecordings.size}`);
  
  const stopPromises = [];
  for (const [anchorId, recordingInfo] of activeRecordings.entries()) {
    if (recordingInfo.ffmpegProcess && !recordingInfo.ffmpegProcess.killed && !recordingInfo.isStopping) {
      log(LOG_LEVEL.INFO, `停止录制: ${anchorId} (${recordingInfo.anchorInfo.anchor_name})`, anchorId);
      recordingInfo.isStopping = true;
      recordingInfo.ffmpegProcess.kill('SIGINT');
      
      if (recordingInfo.recordingPromise) {
        stopPromises.push(
          recordingInfo.recordingPromise.catch(err => {
            log(LOG_LEVEL.WARN, `录制任务结束时发生错误: ${err.message}`, anchorId);
          })
        );
      }
    }
  }
  
  if (stopPromises.length > 0) {
    await Promise.allSettled(stopPromises);
    log(LOG_LEVEL.INFO, `已停止所有录制任务`);
  }
}

/**
 * 从 does 表中读取对应 PROGRAM_TAG 字段的范围，并更新 currentOffset/currentLimit
 */
async function updateRangeFromDoes() {
  try {
    const { data, error } = await supabase
      .from('does')
      .select(`${PROGRAM_TAG}, scanall`)
      .single();

    if (error) {
      console.error(`[${getCurrentTime()}] 从 does 表读取范围时出错: ${error.message}`);
      return;
    }

    const rangeStr = data[PROGRAM_TAG]; // 例如 "[100,550)"
    const match = rangeStr.match(/[\[(](\d+),(\d+)[\])]/);
    if (!match) {
      console.error(`[${getCurrentTime()}] does表中${PROGRAM_TAG}字段的范围格式不正确: ${rangeStr}`);
      return;
    }

    const start = parseInt(match[1], 10);
    const end = parseInt(match[2], 10);
    currentOffset = start;
    currentLimit = end - start;

    console.log(`[${getCurrentTime()}] 当前作用范围: ${rangeStr}, offset=${currentOffset}, limit=${currentLimit}`);
  } catch (err) {
    console.error(`[${getCurrentTime()}] 更新范围时发生异常: ${err.message}`);
  }
}

/**
 * 更新 anchors 表中指定 anchor_id 的指定字段
 */
async function updateAnchorStatus(anchorId, updates) {
  try {
    // 先获取主播名称和当前字段值（用于日志记录）
    let anchorName = '';
    let currentValues = {};
    try {
      const fieldsToGet = ['anchor_name', ...Object.keys(updates)];
      const { data, error } = await supabase
        .from('anchors')
        .select(fieldsToGet.join(','))
        .eq('anchor_id', anchorId)
        .single();
      
      if (!error && data) {
        anchorName = data.anchor_name || '';
        // 保存当前值用于日志
        Object.keys(updates).forEach(key => {
          if (key !== 'anchor_name') {
            currentValues[key] = data[key];
          }
        });
      }
    } catch (err) {
      // 即使获取名称失败也继续更新状态
      console.error(`[${getCurrentTime()}] 获取主播 ${anchorId} 的信息时出错: ${err.message}`);
    }
    
    // 如果更新 live_url，记录详细信息
    if (updates.live_url !== undefined) {
      await appendAnchorLog(anchorId, `准备更新 live_url: 当前值="${currentValues.live_url || '(空)'}" -> 新值="${updates.live_url}"`);
    }

    const { error } = await supabase
      .from('anchors')
      .update(updates)
      .eq('anchor_id', anchorId);

    if (error) {
      console.error(`[${getCurrentTime()}] 更新主播 ${anchorId}${anchorName ? `(${anchorName})` : ''} 的状态时出错: ${error.message}`);
      if (updates.live_url !== undefined) {
        await appendAnchorLog(anchorId, `更新 live_url 失败: ${error.message}`);
      }
    } else {
      console.log(`[${getCurrentTime()}] 成功更新主播 ${anchorId}${anchorName ? `(${anchorName})` : ''} 的状态: ${JSON.stringify(updates)}`);
      if (updates.live_url !== undefined) {
        await appendAnchorLog(anchorId, `成功更新 live_url: "${updates.live_url}"`);
      }
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 更新主播状态时发生异常: ${err.message}`);
    if (updates.live_url !== undefined) {
      await appendAnchorLog(anchorId, `更新 live_url 时发生异常: ${err.message}`);
    }
  }
}

/**
 * 更新主播的 tag 字段
 * @param {string} anchorId 主播ID
 * @param {string} operation 操作类型: 'add' 或 'remove'
 */
async function updateAnchorTag(anchorId, operation) {
  try {
    // 获取当前 tag
    const { data, error } = await supabase
      .from('anchors')
      .select('tag')
      .eq('anchor_id', anchorId)
      .single();
    
    if (error) {
      console.error(`[${getCurrentTime()}] 获取主播 ${anchorId} 的 tag 时出错: ${error.message}`);
      return;
    }
    
    let currentTag = data?.tag || null;
    let newTag = currentTag;
    
    if (operation === 'add') {
      // 添加 tag
      if (!currentTag || currentTag === null || currentTag === '') {
        // tag 为空，直接设置为 PROGRAM_TAG
        newTag = PROGRAM_TAG;
      } else if (currentTag && currentTag.includes('/')) {
        // tag 包含斜杠（多程序标记），总是追加本程序的 tag
        newTag = currentTag + '/' + PROGRAM_TAG;
      } else if (currentTag && !currentTag.split('/').includes(PROGRAM_TAG)) {
        // tag 不为空且不包含当前程序的 tag，追加
        newTag = currentTag + '/' + PROGRAM_TAG;
      }
      // 如果已经包含当前程序的 tag，则不做任何操作
    } else if (operation === 'remove') {
      // 移除 tag
      if (!currentTag || currentTag === '' || currentTag === null) {
        // tag 已经为空，无需操作
        return;
      } else if (currentTag && typeof currentTag === 'string' && currentTag.includes('/')) {
        // 有多个 tag（包含斜杠），保留作为证据，完全不删除
        console.log(`[${getCurrentTime()}] 主播 ${anchorId} 被多个程序录制过 (${currentTag})，保留 tag 作为证据`);
        return;
      } else if (currentTag === PROGRAM_TAG) {
        // tag 完全等于我们的 PROGRAM_TAG，删除它
        newTag = null;
      } else {
        // 其他情况（tag 不为空但也不是我们的 PROGRAM_TAG），不做操作
        console.log(`[${getCurrentTime()}] 主播 ${anchorId} 的 tag (${currentTag}) 不是本程序的标记 (${PROGRAM_TAG})，保留不删除`);
        return;
      }
    }
    
    // 更新 tag
    if (newTag !== currentTag) {
      const { error: updateError } = await supabase
        .from('anchors')
        .update({ tag: newTag })
        .eq('anchor_id', anchorId);
      
      if (updateError) {
        console.error(`[${getCurrentTime()}] 更新主播 ${anchorId} 的 tag 时出错: ${updateError.message}`);
      } else {
        const displayOldTag = currentTag || '(空)';
        const displayNewTag = newTag || '(空)';
        console.log(`[${getCurrentTime()}] 成功更新主播 ${anchorId} 的 tag: ${displayOldTag} -> ${displayNewTag}`);
        await appendAnchorLog(anchorId, `更新 tag: ${displayOldTag} -> ${displayNewTag}`);
      }
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 更新主播 tag 时发生异常: ${err.message}`);
  }
}

/**
 * 增加计数
 */
async function incrementCount(anchorId, countType) {
  try {
    const { data, error } = await supabase
      .from('anchors')
      .select(countType)
      .eq('anchor_id', anchorId)
      .single();

    if (error) {
      console.error(`[${getCurrentTime()}] 获取主播 ${anchorId} 的 ${countType} 时出错: ${error.message}`);
      return;
    }

    const currentCount = data[countType] || 0;
    const newCount = currentCount + 1;

    const { error: updateError } = await supabase
      .from('anchors')
      .update({ [countType]: newCount })
      .eq('anchor_id', anchorId);

    if (updateError) {
      console.error(`[${getCurrentTime()}] 增加主播 ${anchorId} 的 ${countType} 时出错: ${updateError.message}`);
    } else {
      console.log(`[${getCurrentTime()}] 主播 ${anchorId} 的 ${countType} 增加到 ${newCount}`);
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 增加计数时发生异常: ${err.message}`);
  }
}

/**
 * 当 does 表的 PROGRAM_TAG 字段或 scanall 字段发生变化时，更新范围并检查遗漏录制
 */
const onDoesChange = async (payload) => {
  const newRecord = payload.new;
  const oldRecord = payload.old;

  let rangeChanged = false;
  let scanallChanged = false;

  if (oldRecord[PROGRAM_TAG] !== newRecord[PROGRAM_TAG]) {
    log(LOG_LEVEL.INFO, `does 表的 ${PROGRAM_TAG} 字段发生变化，更新区间范围...`);
    rangeChanged = true;
  }

  if (oldRecord.scanall !== newRecord.scanall) {
    log(LOG_LEVEL.INFO, `does表的scanall字段发生变化，触发对遗漏录制的检查`);
    log(LOG_LEVEL.INFO, `当前scanall: ${oldRecord.scanall} -> ${newRecord.scanall}`);
    
    // 打印当前正在录制中的条目（只显示 anchor_id、anchor_name 和 platform）
    if (activeRecordings.size === 0) {
      log(LOG_LEVEL.INFO, `目前本机没有正在录制的条目`);
    } else {
      log(LOG_LEVEL.INFO, `本机实际正在录制的主播列表(${activeRecordings.size}个)：`);
      let idx = 1;
      for (const [anchorId, { anchorInfo }] of activeRecordings.entries()) {
        log(LOG_LEVEL.INFO, 
          `${idx++}. anchor_id: ${anchorId}, anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}`
        );
      }
    }
    
    // 查询并显示当前所有录制状态
    try {
      // 1. 查询当前区间内所有主播的状态
      const { data: allAnchors, error: queryError } = await supabase
        .from('anchors')
        .select('anchor_id, anchor_name, platform, status, is_recording, finishtime, ordertime')
        .range(currentOffset, currentOffset + currentLimit - 1);

      if (queryError) {
        log(LOG_LEVEL.ERROR, `查询主播状态时出错: ${queryError.message}`);
      } else if (allAnchors) {
        // 分类统计
        const statusTrue = allAnchors.filter(a => a.status === true);
        const statusTrueNotRecording = statusTrue.filter(a => a.is_recording === false);
        
        // 显示status=true但未录制的主播
        if (statusTrueNotRecording.length > 0) {
          log(LOG_LEVEL.INFO, `数据库中status=true但未录制的主播(${statusTrueNotRecording.length}个):`);
          statusTrueNotRecording.forEach((anchor, idx) => {
            let timeInfo = "";
            const currentTime = new Date();
            
            if (anchor.finishtime) {
              const finishTime = new Date(anchor.finishtime);
              const timeDiff = Math.round((currentTime - finishTime) / 1000);
              timeInfo = `距finishtime: ${timeDiff}秒`;
            } else if (anchor.ordertime) {
              const orderTime = new Date(anchor.ordertime);
              const timeDiff = Math.round((currentTime - orderTime) / 1000);
              timeInfo = `距ordertime: ${timeDiff}秒`;
            } else {
              timeInfo = "无时间记录";
            }
            
            log(LOG_LEVEL.INFO, `${idx+1}. anchor_id: ${anchor.anchor_id}, anchor_name: ${anchor.anchor_name}, platform: ${anchor.platform}, ${timeInfo}`);
          });
        } else {
          log(LOG_LEVEL.INFO, `没有发现status=true但未录制的主播`);
        }
        
        // 显示状态摘要
        log(LOG_LEVEL.INFO, `当前录制状态摘要:
        - 区间内总主播数: ${allAnchors.length}
        - status=true的主播: ${statusTrue.length}
        - 实际正在录制中: ${activeRecordings.size}
        - 待录制主播数量: ${statusTrueNotRecording.length}`);
      }
    } catch (err) {
      log(LOG_LEVEL.ERROR, `查询录制状态时发生异常: ${err.message}`);
    }

    scanallChanged = true;
  }

  if (rangeChanged) {
    await updateRangeFromDoes();
    // 注意：rangeChanged 也要检查是否遗漏
    await checkAndRestartMissedRecordings();
  }

  if (scanallChanged) {
    // 如果scanall有变化，也要重新检查遗漏条目
    await checkAndRestartMissedRecordings();
  }
};

/**
 * 验证是否为合法URL
 */
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (err) {
    return false;
  }
}


/**
 * 对指定 anchor_id 的链接开始录制
 */
async function startRecording(anchorId, streamUrlData) {
  try {
    // 记录开始录制任务
    await appendAnchorLog(anchorId, `开始录制任务`);
    // 检查是否已经在录制中
    if (activeRecordings.has(anchorId)) {
      const existing = activeRecordings.get(anchorId);
      // 如果存在活跃的FFmpeg进程，跳过
      if (existing.ffmpegProcess && !existing.ffmpegProcess.killed) {
        log(LOG_LEVEL.WARN, `主播 ${anchorId} 已在录制中且进程活跃，跳过重复启动`, anchorId);
        await appendAnchorLog(anchorId, `已在录制中且进程活跃，跳过重复启动`);
        return;
      }
      // 如果是占位条目或进程已结束，允许重新录制
      log(LOG_LEVEL.INFO, `主播 ${anchorId} 存在录制记录但进程已结束，允许重新录制`, anchorId);
      await appendAnchorLog(anchorId, `存在录制记录但进程已结束，允许重新录制`);
    }
    
    // 立即设置占位条目，防止并发调用导致重复录制
    activeRecordings.set(anchorId, { 
      ffmpegProcess: null, 
      recordingPromise: null, 
      anchorInfo: { anchor_id: anchorId },
      isStopping: false,
      placeholder: true, // 标记为占位条目
      startTime: new Date()
    });
    
    // 移除磁盘空间检查，让清理脚本自动处理空间问题
    // if (isDiskSpaceLow) {
    //   log(LOG_LEVEL.WARN, `磁盘空间不足，拒绝启动新的录制任务`, anchorId);
    //   activeRecordings.delete(anchorId); // 删除占位条目
    //   return;
    // }
    
    // 解析stream_url数据
    let streamUrlObject;
    try {
      // 尝试将字符串解析为JSON对象
      if (typeof streamUrlData === 'string') {
        // 检查是否为简单的URL而不是JSON字符串
        if (streamUrlData.startsWith('http')) {
          // 如果是普通URL，创建简单的流对象
          streamUrlObject = { hls_pull_url: streamUrlData };
        } else {
          // 尝试解析JSON
          streamUrlObject = JSON.parse(streamUrlData);
        }
      } else if (typeof streamUrlData === 'object') {
        streamUrlObject = streamUrlData;
      } else {
        throw new Error(`无效的流数据类型: ${typeof streamUrlData}`);
      }
    } catch (err) {
      log(LOG_LEVEL.WARN, `解析流URL数据失败 - anchorId: ${anchorId}, 错误: ${err.message}, 数据: ${typeof streamUrlData === 'string' ? streamUrlData.substring(0, 100) : JSON.stringify(streamUrlData).substring(0, 100)}...`, anchorId);
      await appendAnchorLog(anchorId, `解析流URL数据失败: ${err.message}`);
      activeRecordings.delete(anchorId); // 删除占位条目
      return;
    }
    
    // 处理嵌套的 stream_url 结构（如数据库中的格式）
    if (streamUrlObject && streamUrlObject.stream_url && typeof streamUrlObject.stream_url === 'object') {
      log(LOG_LEVEL.INFO, `检测到嵌套的 stream_url 结构，使用内层 stream_url`, anchorId);
      streamUrlObject = streamUrlObject.stream_url;
    }
    
    // 如果没有任何可用URL，跳过录制
    // 检查顶层URL字段和嵌套的extra字段（用于音频直播）
    const hasTopLevelUrl = streamUrlObject && (
      streamUrlObject.hls_pull_url || 
      streamUrlObject.hls_pull_url_map || 
      streamUrlObject.flv_pull_url ||
      (typeof streamUrlObject.flv_pull_url === 'object' && Object.keys(streamUrlObject.flv_pull_url).length > 0)
    );
    
    const hasExtraUrl = streamUrlObject && streamUrlObject.extra && (
      streamUrlObject.extra.flv_pull_url || 
      streamUrlObject.extra.hls_pull_url || 
      streamUrlObject.extra.hls_pull_url_map
    );
    
    // 检查live_core_sdk_data字段（用于新格式的音频直播）
    const hasLiveCoreUrl = streamUrlObject && streamUrlObject.live_core_sdk_data && 
      streamUrlObject.live_core_sdk_data.pull_data && 
      streamUrlObject.live_core_sdk_data.pull_data.stream_data;
    
    if (!streamUrlObject || (!hasTopLevelUrl && !hasExtraUrl && !hasLiveCoreUrl)) {
      log(LOG_LEVEL.WARN, `无可用的流URL - anchorId: ${anchorId}, streamUrlObject: ${JSON.stringify(streamUrlObject).substring(0, 200)}...`, anchorId);
      await appendAnchorLog(anchorId, `无可用的流URL`);
      activeRecordings.delete(anchorId); // 删除占位条目
      return;
    }

    // 先获取 anchor_name、platform、title、ordertime 和 new_url 用于后续打印
    let anchorInfo = { anchor_name: '', platform: '', title: '', ordertime: null, _new_url: '' };
    const { data: anchorData, error } = await supabase
      .from('anchors')
      .select('anchor_name, platform, title, ordertime, new_url')
      .eq('anchor_id', anchorId)
      .single();

    if (error) {
      console.error(`[${getCurrentTime()}] 未能获取主播 ${anchorId} 的 anchor_name, platform: ${error.message}`);
      await appendAnchorLog(anchorId, `未能获取主播信息: ${error.message}`);
      // 严重错误时可以选择继续录制或放弃
      // 这里选择继续，使用默认值
    } else {
      anchorInfo = { 
        anchor_name: anchorData.anchor_name || '', 
        platform: anchorData.platform || '',
        title: anchorData.title || '',
        ordertime: anchorData.ordertime || null,
        _new_url: anchorData.new_url || ''
      };
      // 打印new_url信息
      if (anchorData.new_url) {
        log(LOG_LEVEL.INFO, `主播 ${anchorId} (${anchorData.anchor_name}) 的 new_url: ${anchorData.new_url}`, anchorId);
      }
    }

    // 如果stream_url对象中有_new_url，优先使用它
    if (streamUrlObject._new_url) {
      anchorInfo._new_url = streamUrlObject._new_url;
    }
    
    // 如果stream_url对象中有_anchor_name且anchorInfo的anchor_name为空，使用它
    if (streamUrlObject._anchor_name && !anchorInfo.anchor_name) {
      anchorInfo.anchor_name = streamUrlObject._anchor_name;
    }

    // 更新已存在的占位条目，添加更多信息
    if (activeRecordings.has(anchorId)) {
      const recording = activeRecordings.get(anchorId);
      recording.anchorInfo = anchorInfo;
      recording.latestOutputFile = null;
      delete recording.placeholder; // 移除占位标记
    }

    // 获取最佳录制URL用于记录
    await appendAnchorLog(anchorId, `开始解析stream_url数据，提取最佳录制地址`);
    let bestStreamUrl = extractBestStreamUrl(streamUrlObject, null, anchorInfo.platform);
    if (bestStreamUrl) {
      await appendAnchorLog(anchorId, `成功解析出录制URL: ${bestStreamUrl}`);
      // 更新 live_url 字段，覆盖原有内容
      await updateAnchorStatus(anchorId, { live_url: bestStreamUrl });
      log(LOG_LEVEL.INFO, `已更新主播 ${anchorId} 的 live_url: ${bestStreamUrl}`, anchorId);
      await appendAnchorLog(anchorId, `已更新 live_url: ${bestStreamUrl}`);
    } else {
      await appendAnchorLog(anchorId, `警告: 无法从stream_url解析出有效的录制地址`);
      log(LOG_LEVEL.WARN, `无法从stream_url解析出有效的录制地址`, anchorId);
    }
    
    // 记录开始调用recordStream
    await appendAnchorLog(anchorId, `调用recordStream准备开始录制，主播名: ${anchorInfo.anchor_name}, 平台: ${anchorInfo.platform}`);
    
    // 调用recordStream进行实际录制
    // 重要修复：不要await recordStream，让它在后台异步运行，避免阻塞事件循环
    recordStream(streamUrlObject, anchorId, anchorInfo)
      .then(async ({ ffmpegProcess, recordingPromise, isStopping }) => {
        // 录制启动成功，更新activeRecordings中的详细信息
        await appendAnchorLog(anchorId, `录制启动成功`);
        if (activeRecordings.has(anchorId)) {
          const recording = activeRecordings.get(anchorId);
          recording.ffmpegProcess = ffmpegProcess;
          recording.recordingPromise = recordingPromise;
          recording.isStopping = isStopping;
        }

        // 处理录制结束
        recordingPromise
          .then(async () => {
            activeRecordings.delete(anchorId);
            await updateAnchorStatus(anchorId, { is_recording: false });
            await updateAnchorTag(anchorId, 'remove');
          })
          .catch(async (err) => {
            console.error(
              `[${getCurrentTime()}] 录制过程中出现异常 (anchor_id: ${anchorId}, anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}): ${err.message}`
            );
            await appendAnchorLog(anchorId, `录制过程中出现异常: ${err.message}`);
            activeRecordings.delete(anchorId);
            await updateAnchorStatus(anchorId, { is_recording: false });
            await updateAnchorTag(anchorId, 'remove');
          });
      })
      .catch(async err => {
        console.error(`[${getCurrentTime()}] 启动录制失败 (anchor_id: ${anchorId}): ${err.message}`);
        await appendAnchorLog(anchorId, `启动录制失败: ${err.message}`);
        activeRecordings.delete(anchorId);
        await updateAnchorStatus(anchorId, { is_recording: false });
        await updateAnchorTag(anchorId, 'remove');
      });

    // 不再需要下面的代码，因为已经移到then回调中
    return;
    
    // 这部分代码已经移到上面的then回调中
  } catch (err) {
    console.error(`[${getCurrentTime()}] 开始录制主播 ${anchorId} 时出错: ${err.message}`);
    await appendAnchorLog(anchorId, `开始录制时出错: ${err.message}`);
    // 出错时删除占位条目
    activeRecordings.delete(anchorId);
    await updateAnchorStatus(anchorId, { is_recording: false });
    await updateAnchorTag(anchorId, 'remove');
  }
}

/**
 * 从流对象中提取最佳可用URL
 * @param {object} streamObj 流对象
 * @param {string} quality 指定清晰度，为null时按优先级选择
 * @param {string} platform 平台标识
 * @returns {string|null} URL或null
 */
function extractBestStreamUrl(streamObj, quality = null, platform = '') {
  // 如果传入的是简单URL字符串，且是HLS，直接返回
  if (typeof streamObj === 'string' && streamObj.startsWith('http') && (streamObj.includes('.m3u8') || streamObj.includes('/hls/'))) {
    return streamObj;
  } else if (typeof streamObj === 'string') {
    // 如果是字符串但不是HLS，则认为无效
    return null;
  }
  
  if (!streamObj) return null;
  
  // 处理嵌套的 stream_url 结构（如数据库中的格式）
  if (streamObj.stream_url && typeof streamObj.stream_url === 'object') {
    log(LOG_LEVEL.DEBUG, `检测到嵌套的 stream_url 结构，使用内层 stream_url`);
    streamObj = streamObj.stream_url;
  }
  
  // 处理嵌套在live_core_sdk_data.pull_data.stream_data中的JSON字符串
  if (streamObj.live_core_sdk_data && streamObj.live_core_sdk_data.pull_data && 
      streamObj.live_core_sdk_data.pull_data.stream_data) {
    try {
      const streamData = JSON.parse(streamObj.live_core_sdk_data.pull_data.stream_data);
      const backupUrls = [];
      
      // 只提取最高画质(origin)的HLS链接
      if (streamData.data && streamData.data.origin && streamData.data.origin.main && 
          streamData.data.origin.main.hls) {
        log(LOG_LEVEL.DEBUG, `从live_core_sdk_data中提取到蓝光HLS链接`);
        const hlsUrl = streamData.data.origin.main.hls;
        backupUrls.push({url: hlsUrl, quality: 'origin', type: 'hls'});
      }
      // 可以考虑再提取一个明确的次高画质HLS，如md，以防origin在某些情况下解析不正确或不可用
      if (streamData.data && streamData.data.md && streamData.data.md.main && 
          streamData.data.md.main.hls) {
        log(LOG_LEVEL.DEBUG, `从live_core_sdk_data中提取到md画质HLS链接作为备用`);
        const hlsUrl = streamData.data.md.main.hls;
        backupUrls.push({url: hlsUrl, quality: 'md', type: 'hls'});
      }      
      
      if (backupUrls.length > 0) {
        backupUrls.forEach((item, index) => {
          log(LOG_LEVEL.DEBUG, `[extractBestStreamUrl] 备用HLS链接 ${index+1}: 画质=${item.quality}, URL=${item.url.substring(0, 100)}...`);
        });
        streamObj._backup_urls = backupUrls; // 存储所有提取到的HLS备用链接
        return backupUrls[0].url; // 返回最高优先级的HLS链接
      }
    } catch (err) {
      log(LOG_LEVEL.ERROR, `解析stream_data失败: ${err.message}`);
    }
    // 如果live_core_sdk_data解析失败或无HLS，尝试从顶层结构获取
  }
  
  // 清晰度优先级（仅HLS相关）：FULL_HD1 > HD1 > SD1 > SD2
  const qualityPriority = ['FULL_HD1', 'HD1', 'SD1', 'SD2'];
  const douyinQualityMap = {
    'origin': 'FULL_HD1', 'hd': 'HD1', 'sd': 'SD1', 'ld': 'SD2'
  };
  
  let bestHlsUrl = null;

  // 优先处理抖音平台的hls_pull_url_map
  if (platform === 'douyin' && streamObj.hls_pull_url_map) {
    const douyinPriority = ['origin', 'hd', 'sd', 'ld'];
    for (const qKey of douyinPriority) {
      if (streamObj.hls_pull_url_map[qKey]) {
        const standardQuality = douyinQualityMap[qKey] || 'FULL_HD1';
        log(LOG_LEVEL.DEBUG, `抖音平台使用最高可用HLS清晰度: ${qKey} (映射为: ${standardQuality})`);
        return streamObj.hls_pull_url_map[qKey];
      }
    }
  }
  
  // 标准HLS URL提取 (hls_pull_url_map)
  if (streamObj.hls_pull_url_map) {
    for (const q of qualityPriority) {
      if (streamObj.hls_pull_url_map[q]) {
        log(LOG_LEVEL.DEBUG, `选择HLS清晰度: ${q}`);
        return streamObj.hls_pull_url_map[q];
      }
    }
  }
  
  // 尝试直接使用顶层的hls_pull_url (如果存在且是HLS)
  if (streamObj.hls_pull_url && (streamObj.hls_pull_url.includes('.m3u8') || streamObj.hls_pull_url.includes('/hls/'))) {
    log(LOG_LEVEL.DEBUG, `使用默认HLS流: ${streamObj.hls_pull_url}`);
    return streamObj.hls_pull_url;
  }
  
  // 如果前面所有尝试都失败了，检查之前在live_core_sdk_data中是否存有备用链接
  if (streamObj._backup_urls && streamObj._backup_urls.length > 0) {
    log(LOG_LEVEL.DEBUG, '无直接HLS链接，使用从live_core_sdk_data提取的备用HLS链接');
    return streamObj._backup_urls[0].url; 
  }

  // 检查extra字段中的流URL（音频直播使用）
  if (streamObj.extra) {
    // 优先检查extra中的hls_pull_url_map
    if (streamObj.extra.hls_pull_url_map) {
      const qualities = ['FULL_HD1', 'HD1', 'SD1', 'SD2'];
      for (const q of qualities) {
        if (streamObj.extra.hls_pull_url_map[q]) {
          log(LOG_LEVEL.DEBUG, `使用extra字段中的HLS URL - 清晰度: ${q}（音频直播）`);
          return streamObj.extra.hls_pull_url_map[q];
        }
      }
    }
    
    // 检查extra中的hls_pull_url
    if (streamObj.extra.hls_pull_url && (streamObj.extra.hls_pull_url.includes('.m3u8') || streamObj.extra.hls_pull_url.includes('/hls/'))) {
      log(LOG_LEVEL.DEBUG, `使用extra字段中的默认HLS流（音频直播）`);
      return streamObj.extra.hls_pull_url;
    }
    
    // 最后才检查extra中的flv_pull_url
    if (streamObj.extra.flv_pull_url) {
      if (typeof streamObj.extra.flv_pull_url === 'string') {
        log(LOG_LEVEL.DEBUG, `使用extra字段中的FLV URL作为备选（音频直播）`);
        return streamObj.extra.flv_pull_url;
      } else if (typeof streamObj.extra.flv_pull_url === 'object') {
        // 如果是对象，尝试获取最高质量的URL
        const qualities = ['FULL_HD1', 'HD1', 'SD1', 'SD2'];
        for (const q of qualities) {
          if (streamObj.extra.flv_pull_url[q]) {
            log(LOG_LEVEL.DEBUG, `使用extra字段中的FLV URL作为备选 - 清晰度: ${q}（音频直播）`);
            return streamObj.extra.flv_pull_url[q];
          }
        }
      }
    }
  }

  log(LOG_LEVEL.WARN, `无法从流对象中提取任何有效的HLS URL: ${JSON.stringify(streamObj).substring(0,200)}`);
  return null;
}

/**
 * 获取流对象中指定清晰度索引的链接
 * @param {object} streamObj 流对象
 * @param {number} qualityIndex 清晰度索引，0为最高清晰度，越大清晰度越低
 * @returns {string|null} 链接或null
 */
function getUrlByQualityIndex(streamObj, qualityIndex) {
  if (!streamObj) return null;
  
  const qualityPriority = ['FULL_HD1', 'HD1', 'SD1', 'SD2'];
  
  // 如果索引超出范围，返回null
  if (qualityIndex < 0 || qualityIndex >= qualityPriority.length) return null;
  
  // 优先尝试HLS
  if (streamObj.hls_pull_url_map && streamObj.hls_pull_url_map[qualityPriority[qualityIndex]]) {
    return streamObj.hls_pull_url_map[qualityPriority[qualityIndex]];
  }
  
  // 其次尝试FLV
  if (streamObj.flv_pull_url && typeof streamObj.flv_pull_url === 'object' && 
      streamObj.flv_pull_url[qualityPriority[qualityIndex]]) {
    return streamObj.flv_pull_url[qualityPriority[qualityIndex]];
  }
  
  // 如果指定清晰度没有，返回null
  return null;
}

/**
 * 验证HLS流的可用性
 * @param {string} url HLS流地址
 * @returns {Promise<boolean>} 是否可用
 */
async function validateHlsStream(url, anchorId = '') { // 添加 anchorId 以便日志关联
  try {
    const isDouyinUrl = url.includes('douyincdn.com') || 
                        url.includes('douyinliving.com') || 
                        url.includes('douyinvod.com') ||
                        url.includes('flive.douyincdn.com'); // 增加了 flive.douyincdn.com
    
    if (isDouyinUrl) {
      const expireMatch = url.match(/expire=(\d+)/);
      if (expireMatch) {
        const expireTimestamp = parseInt(expireMatch[1], 10);
        const currentTimestamp = Math.floor(Date.now() / 1000);
        if (expireTimestamp <= currentTimestamp + 300) { // 提前5分钟判断为即将过期
          log(LOG_LEVEL.WARN, `HLS流已过期或即将过期 (expire=${expireTimestamp}, current=${currentTimestamp})，判定无效: ${url}`, anchorId);
          return false;
        }
      }
    }
  
    const cmd = `ffprobe -v error -probesize 20M -analyzeduration 15M -show_entries stream=codec_type -select_streams v:0 -of json -i "${url}" -timeout 30000000`; // 降低探测大小和时间，增加超时
    const { stdout, stderr } = await execPromise(cmd, { timeout: 45000 }); // 给ffprobe本身也设置一个执行超时
    
    if (stderr) {
        // FFprobe的stderr输出通常包含了错误的关键信息
        if (stderr.includes('404 Not Found') || stderr.includes('Server returned 404 Not Found')) {
            log(LOG_LEVEL.WARN, `HLS流返回404错误 (ffprobe stderr)，判定无效: ${url}`, anchorId);
            return false;
        }
        if (stderr.includes('403 Forbidden') || stderr.includes('Server returned 403 Forbidden')) {
            log(LOG_LEVEL.WARN, `HLS流返回403禁止访问 (ffprobe stderr)，判定无效: ${url}`, anchorId);
            return false;
        }
        // 其他stderr内容也可能指示问题，但暂时不直接判false，除非下面JSON解析也失败
        log(LOG_LEVEL.DEBUG, `HLS验证ffprobe stderr: ${stderr.substring(0,100)}`, anchorId)
    }

    try {
      const result = JSON.parse(stdout);
      if (result.streams && result.streams.length > 0 && result.streams.some(stream => stream.codec_type === 'video')) {
        log(LOG_LEVEL.INFO, `HLS流验证成功: ${url}`, anchorId);
        return true;
      }
      log(LOG_LEVEL.WARN, `HLS流没有找到视频流 (JSON解析成功但无视频): ${url}`, anchorId);
      return false;
    } catch (parseErr) {
      if (stdout && stdout.includes('Video')) { // 作为最后的补救措施
        log(LOG_LEVEL.INFO, `HLS流验证成功 (非JSON响应但包含Video关键词): ${url}`, anchorId);
        return true;
      }
      log(LOG_LEVEL.ERROR, `HLS流验证JSON解析失败 (${parseErr.message})，stdout: ${stdout.substring(0,100)}，判定无效: ${url}`, anchorId);
      return false;
    }
  } catch (err) {
    // 这个catch块捕获 execPromise 的直接错误 (例如超时，或ffprobe命令不存在等)
    if (err.message.includes('404 Not Found') || err.message.includes('Server returned 404 Not Found')) {
      log(LOG_LEVEL.WARN, `HLS流返回404错误 (execPromise catch)，判定无效: ${url}`, anchorId);
    } else if (err.message.includes('403 Forbidden') || err.message.includes('Server returned 403 Forbidden')) {
      log(LOG_LEVEL.WARN, `HLS流返回403禁止访问 (execPromise catch)，判定无效: ${url}`, anchorId);
    } else if (err.timedOut) {
      log(LOG_LEVEL.WARN, `HLS流验证ffprobe执行超时，判定无效: ${url}`, anchorId);
    } else {
      log(LOG_LEVEL.ERROR, `HLS流验证执行失败 (execPromise catch): ${err.message.substring(0,100)}... URL: ${url}`, anchorId);
    }
    return false;
  }
}

/**
 * 从HLS流中检测最佳分辨率
 * @param {string} url HLS流地址
 * @returns {Promise<string|null>} 最佳分辨率或null
 */
async function detectBestHlsResolution(url) {
  try {
    const cmd = `ffprobe -v error -show_entries stream=width,height -of json -i "${url}"`;
    const { stdout } = await execPromise(cmd);
    const result = JSON.parse(stdout);
    
    let maxWidth = 0;
    let bestResolution = null;
    
    if (result.streams) {
      for (const stream of result.streams) {
        if (stream.width && stream.width > maxWidth) {
          maxWidth = stream.width;
          bestResolution = `${stream.width}x${stream.height}`;
        }
      }
    }
    
    if (bestResolution) {
      console.log(`[${getCurrentTime()}] 检测到HLS流最佳分辨率: ${bestResolution}, URL: ${url}`);
      return bestResolution;
    }
    
    return null;
  } catch (err) {
    console.error(`[${getCurrentTime()}] 检测HLS分辨率失败: ${err.message}, URL=${url}`);
    return null;
  }
}

/**
 * 从数据库获取主播最新的流链接
 * @param {string} anchorId 主播ID
 * @returns {Promise<object|string|null>} 流链接对象或null
 */
async function getLatestStreamUrl(anchorId) {
  try {
    return await withRetry(async () => {
      const { data, error } = await supabase
        .from('anchors')
        .select('stream_url, anchor_name, new_url')
        .eq('anchor_id', anchorId)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      if (!data || !data.stream_url) {
        log(LOG_LEVEL.WARN, `未找到主播流链接`, anchorId);
        return null;
      }

      let streamUrl = data.stream_url;
      const anchorName = data.anchor_name || '';
      const newUrl = data.new_url || '';
      
      // 解析stream_url
      try {
        let parsed;
        if (typeof streamUrl === 'string') {
          if (streamUrl.startsWith('http')) {
            parsed = streamUrl;
          } else {
            parsed = JSON.parse(streamUrl);
          }
        } else {
          parsed = streamUrl;
        }
        
        // 提取最佳URL用于去重比较
        const bestUrl = typeof parsed === 'string' ? parsed : extractBestStreamUrl(parsed);
        
        // 检查是否与上次URL相同
        if (lastUsedUrls.has(anchorId) && lastUsedUrls.get(anchorId) === bestUrl) {
          log(LOG_LEVEL.WARN, `获取到的链接与上次相同，可能需要等待平台更新 (anchor_name: ${anchorName})`, anchorId);
          // 如果连续获取到相同URL，等待更长时间避免频繁重试
          await new Promise(r => setTimeout(r, 10000));
        } else if (bestUrl) {
          // 记录新的URL
          lastUsedUrls.set(anchorId, bestUrl);
        }
        
        // 将new_url添加到返回结果中
        if (typeof parsed === 'object') {
          parsed._new_url = newUrl;
          parsed._anchor_name = anchorName;
        }
        
        return parsed;
      } catch (e) {
        throw new Error(`解析流链接失败: ${e.message}`);
      }
    }, 3, '获取流链接', anchorId);
  } catch (err) {
    logError(`获取最新流链接时发生异常`, err, anchorId);
    return null;
  }
}

/**
 * 从流对象中提取特定画质的URL
 * @param {object} streamObj 流对象
 * @param {string} qualityLevel 需要的画质级别
 * @param {boolean} preferHls 是否优先HLS
 * @returns {string|null} URL或null
 */
function extractSpecificQualityUrl(streamObj, qualityLevel, preferHls = true) {
  if (!streamObj || !qualityLevel) return null;
  
  // 首先尝试HLS
  if (preferHls && streamObj.hls_pull_url_map && streamObj.hls_pull_url_map[qualityLevel]) {
    return streamObj.hls_pull_url_map[qualityLevel];
  }
  
  // 其次尝试FLV
  if (streamObj.flv_pull_url && streamObj.flv_pull_url[qualityLevel]) {
    return streamObj.flv_pull_url[qualityLevel];
  }
  
  // 最后尝试默认HLS
  if (preferHls && streamObj.hls_pull_url) {
    return streamObj.hls_pull_url;
  }
  
  return null;
}

/**
 * 从URL中提取画质等级
 * @param {string} url 流URL
 * @returns {string|null} 画质等级或null
 */
function extractQualityFromUrl(url) {
  if (!url) return null;
  
  // 检查URL是否包含明确的画质标识
  // 例如: stream-xxx_uhd.m3u8, stream-xxx_hd.m3u8, stream-xxx_sd.m3u8, stream-xxx_ld.m3u8
  const qualityMap = {
    '_uhd.': 'FULL_HD1',
    '_hd.': 'HD1',
    '_ld.': 'SD1', 
    '_sd.': 'SD2',
    // 抖音平台特有命名
    '_origin.': 'FULL_HD1',
    '_or4.': 'FULL_HD1',   // 蓝光原画
    '_720p.': 'HD1'         // 720p
  };
  
  for (const [suffix, quality] of Object.entries(qualityMap)) {
    if (url.includes(suffix)) {
      return quality;
    }
  }
  
  // 通过链接结构推断
  if (url.includes('FULL_HD1') || url.includes('uhd') || url.includes('origin') || url.includes('or4')) {
    return 'FULL_HD1';
  } else if (url.includes('HD1') || url.includes('hd') || url.includes('720p')) {
    return 'HD1';
  } else if (url.includes('SD1') || url.includes('ld')) {
    return 'SD1';
  } else if (url.includes('SD2') || url.includes('sd')) {
    return 'SD2';
  }
  
  return null;
}

// Cookie 池配置
const COOKIE_POOL_PATH = '/home/<USER>/API/local-singbox/config/cookie_pool_multi.json';

/**
 * 从 cookie 池中随机获取一个 cookie
 * @returns {string|null} 随机选择的 cookie 或 null
 */
function getRandomCookie() {
  try {
    if (fs.existsSync(COOKIE_POOL_PATH)) {
      const data = JSON.parse(fs.readFileSync(COOKIE_POOL_PATH, 'utf-8'));
      const cookies = data.cookies || [];
      if (cookies.length > 0) {
        const selectedCookie = cookies[Math.floor(Math.random() * cookies.length)];
        log(LOG_LEVEL.DEBUG, `从cookie池中选择了一个cookie (共${cookies.length}个)`);
        return selectedCookie;
      } else {
        log(LOG_LEVEL.WARN, 'Cookie池为空');
      }
    } else {
      log(LOG_LEVEL.WARN, `Cookie池文件不存在: ${COOKIE_POOL_PATH}`);
    }
  } catch (err) {
    logError('获取随机cookie失败', err);
  }
  return null;
}

/**
 * 为抖音链接构建HTTP请求头（每次调用都会获取新的随机cookie）
 * @param {string} anchorId - 主播ID，用于日志记录
 * @returns {string|null} 格式化的HTTP头部字符串或null
 */
function buildDouyinHttpHeaders(anchorId) {
  try {
    let headerStr = '';
    
    // 固定的请求头
    headerStr += 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36\r\n';
    headerStr += 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8\r\n';
    headerStr += 'Referer: https://www.douyin.com/\r\n';
    
    // 每次录制都从 cookie 池中获取一个新的随机 cookie
    const randomCookie = getRandomCookie();
    if (randomCookie) {
      headerStr += `Cookie: ${randomCookie}\r\n`;
      log(LOG_LEVEL.INFO, `[录制任务 ${anchorId}] 已选择新的Cookie用于本次录制`, anchorId);
    } else {
      log(LOG_LEVEL.WARN, `[录制任务 ${anchorId}] 无法获取cookie，仅使用基础headers`, anchorId);
    }
    
    return headerStr;
  } catch (err) {
    logError(`[录制任务 ${anchorId}] 构建抖音HTTP头部失败`, err);
    return null;
  }
}

/**
 * 处理主播直播结束的逻辑
 * @param {string} anchorId 主播ID
 * @param {string} anchorName 主播名称
 * @param {string} platform 平台
 * @param {boolean} isRecording 是否正在录制
 */
async function handleAnchorStreamEnd(anchorId, anchorName, platform, isRecording) {
  try {
    // 记录处理主播下播
    await appendAnchorLog(anchorId, `处理主播下播，主播名: ${anchorName}, 平台: ${platform}, is_recording: ${isRecording}`);
    // 文件锁机制已经处理了并发问题，不再需要 cleanupDone
    
    // 1. 检查是否有正在录制的进程
    let recordingInfo = null;
    let hasActiveRecording = activeRecordings.has(anchorId);
    
    if (hasActiveRecording) {
      recordingInfo = activeRecordings.get(anchorId);
    }
    
    // 2. 如果数据库显示仍在录制或者有活跃的录制进程，强制结束
    if (isRecording === true || hasActiveRecording) {
      log(LOG_LEVEL.WARN, `[handleAnchorStreamEnd] 主播 ${anchorId} 下播但仍在录制状态，强制结束录制 (db_is_recording: ${isRecording}, active_recording: ${hasActiveRecording})`);
      await appendAnchorLog(anchorId, `下播但仍在录制状态，强制结束录制`);
      
      if (hasActiveRecording && recordingInfo) {
        // 设置停止标志
        recordingInfo.isStopping = true;
        
        // 强制结束 ffmpeg 进程
        if (recordingInfo.ffmpegProcess && !recordingInfo.ffmpegProcess.killed) {
          log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 发送 SIGINT 信号终止主播 ${anchorId} 的录制进程`);
          await appendAnchorLog(anchorId, `发送 SIGINT 信号终止录制进程`);
          recordingInfo.ffmpegProcess.kill('SIGINT');
          
          // 等待进程结束，设置超时防止卡住
          try {
            if (recordingInfo.recordingPromise) {
              await Promise.race([
                recordingInfo.recordingPromise,
                new Promise((resolve) => setTimeout(resolve, 15000)) // 15秒超时
              ]);
              log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} 录制进程已正常结束`);
              await appendAnchorLog(anchorId, `录制进程已正常结束`);
            }
          } catch (err) {
            log(LOG_LEVEL.WARN, `[handleAnchorStreamEnd] 等待录制结束超时或出错，继续处理: ${err.message}`, anchorId);
            await appendAnchorLog(anchorId, `等待录制结束超时或出错: ${err.message}`);
            
            // 如果等待超时，强制杀死进程
            if (recordingInfo.ffmpegProcess && !recordingInfo.ffmpegProcess.killed) {
              recordingInfo.ffmpegProcess.kill('SIGKILL');
              log(LOG_LEVEL.WARN, `[handleAnchorStreamEnd] 强制杀死主播 ${anchorId} 的录制进程`);
              await appendAnchorLog(anchorId, `强制杀死录制进程`);
            }
          }
        }
      }
      
      // 确保数据库状态更新
      await updateAnchorStatus(anchorId, { is_recording: false });
      await updateAnchorTag(anchorId, 'remove');
    }
    
    // 3. 检查录制目录并处理文件
    if (hasActiveRecording && recordingInfo && recordingInfo.subdir) {
      log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 开始处理主播 ${anchorId} 的录制文件，扫描目录: ${recordingInfo.subdir}`);
      await appendAnchorLog(anchorId, `开始处理录制文件，目录: ${recordingInfo.subdir}`);
      
      // 等待一小段时间确保文件写入完成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 检查目录是否存在以及是否有文件
      const hasValidFiles = await checkRecordingDirectory(anchorId, recordingInfo.subdir);
      
      if (hasValidFiles) {
        // 同步等待文件处理完成，确保文件被发送到数据库
        // 传入 forceProcessAll = true，因为 status = false 时必须处理所有文件
        try {
          await appendAnchorLog(anchorId, `开始同步处理录制文件，等待处理完成...`);
          await processRecordedFiles(anchorId, recordingInfo, true);
          log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} 的录制文件处理成功完成`);
          await appendAnchorLog(anchorId, `录制文件处理成功完成`);
        } catch (err) {
          logError(`[handleAnchorStreamEnd] 处理主播 ${anchorId} 的录制文件时出错`, err, anchorId);
          await appendAnchorLog(anchorId, `处理录制文件失败: ${err.message}`);
          // 记录详细错误信息以便调试
          log(LOG_LEVEL.ERROR, `[handleAnchorStreamEnd] 文件处理错误详情: ${err.stack}`, anchorId);
        }
      } else {
        log(LOG_LEVEL.WARN, `[handleAnchorStreamEnd] 主播 ${anchorId} 录制目录无有效文件，跳过处理`);
        await appendAnchorLog(anchorId, `录制目录无有效文件，跳过处理`);
      }
      
      // 从 activeRecordings 中移除
      activeRecordings.delete(anchorId);
    } else if (hasActiveRecording) {
      log(LOG_LEVEL.WARN, `[handleAnchorStreamEnd] 主播 ${anchorId} 有活跃录制但缺少目录信息，尝试使用默认目录`);
      await appendAnchorLog(anchorId, `有活跃录制但缺少目录信息，尝试使用默认目录`);
      
      // 即使缺少 subdir，也尝试检查默认目录
      const recordingDir = path.join(RECORDING_DIR, anchorId);
      
      try {
        const hasValidFiles = await checkRecordingDirectory(anchorId, recordingDir);
        
        if (hasValidFiles) {
          log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 在默认目录发现主播 ${anchorId} 的录制文件，开始处理`);
          await appendAnchorLog(anchorId, `在默认目录发现录制文件，开始处理`);
          
          // 修补 recordingInfo
          if (!recordingInfo.subdir) {
            recordingInfo.subdir = recordingDir;
          }
          
          // 同步等待文件处理完成
          try {
            await appendAnchorLog(anchorId, `开始同步处理默认目录中的录制文件...`);
            await processRecordedFiles(anchorId, recordingInfo, true);
            log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} 默认目录的录制文件处理成功完成`);
            await appendAnchorLog(anchorId, `默认目录的录制文件处理成功完成`);
          } catch (err) {
            logError(`[handleAnchorStreamEnd] 处理主播 ${anchorId} 的录制文件时出错`, err, anchorId);
            await appendAnchorLog(anchorId, `处理默认目录录制文件失败: ${err.message}`);
            log(LOG_LEVEL.ERROR, `[handleAnchorStreamEnd] 文件处理错误详情: ${err.stack}`, anchorId);
          }
        } else {
          log(LOG_LEVEL.WARN, `[handleAnchorStreamEnd] 主播 ${anchorId} 默认目录无有效文件`);
          await appendAnchorLog(anchorId, `默认目录无有效文件`);
        }
      } catch (err) {
        logError(`[handleAnchorStreamEnd] 检查主播 ${anchorId} 默认目录时出错`, err, anchorId);
        await appendAnchorLog(anchorId, `检查默认目录时出错: ${err.message}`);
      }
      
      activeRecordings.delete(anchorId);
    } else {
      log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} 下播但不在 activeRecordings 中，检查录制目录`);
      await appendAnchorLog(anchorId, `下播但不在 activeRecordings 中，检查录制目录`);
      
      // 即使不在 activeRecordings 中，也要检查录制目录是否有文件需要处理
      const recordingDir = path.join(RECORDING_DIR, anchorId);
      
      try {
        // 检查目录是否存在
        await fs.promises.access(recordingDir);
        
        // 检查是否有有效文件
        const hasValidFiles = await checkRecordingDirectory(anchorId, recordingDir);
        
        if (hasValidFiles) {
          log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 发现主播 ${anchorId} 的录制文件，开始处理`);
          await appendAnchorLog(anchorId, `发现录制文件，开始处理`);
          
          // 查询主播的 title 和 ordertime
          let title = '';
          let ordertime = null;
          try {
            const { data: anchorData, error } = await supabase
              .from('anchors')
              .select('title, ordertime')
              .eq('anchor_id', anchorId)
              .single();
            
            if (!error && anchorData) {
              title = anchorData.title || '';
              ordertime = anchorData.ordertime || null;
            }
          } catch (e) {
            log(LOG_LEVEL.WARN, `[handleAnchorStreamEnd] 查询主播 ${anchorId} 的 title 和 ordertime 失败: ${e.message}`, anchorId);
          }
          
          // 构建 recordingInfo 对象
          const recordingInfo = {
            subdir: recordingDir,
            anchorInfo: {
              anchor_id: anchorId,
              anchor_name: anchorName || '',
              platform: platform || '',
              title: title,
              ordertime: ordertime
            },
            latestOutputFile: null // 没有正在录制的文件
          };
          
          // 同步等待文件处理完成
          try {
            await appendAnchorLog(anchorId, `开始同步处理发现的录制文件...`);
            await processRecordedFiles(anchorId, recordingInfo, true);
            log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} 发现的录制文件处理成功完成`);
            await appendAnchorLog(anchorId, `发现的录制文件处理成功完成`);
          } catch (err) {
            logError(`[handleAnchorStreamEnd] 处理主播 ${anchorId} 的录制文件时出错`, err, anchorId);
            await appendAnchorLog(anchorId, `处理发现的录制文件失败: ${err.message}`);
            log(LOG_LEVEL.ERROR, `[handleAnchorStreamEnd] 文件处理错误详情: ${err.stack}`, anchorId);
          }
        } else {
          log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} 没有录制文件需要处理`);
          await appendAnchorLog(anchorId, `没有录制文件需要处理`);
        }
      } catch (err) {
        if (err.code === 'ENOENT') {
          log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} 的录制目录不存在，无需处理`);
          await appendAnchorLog(anchorId, `录制目录不存在，无需处理`);
        } else {
          logError(`[handleAnchorStreamEnd] 检查录制目录时出错`, err, anchorId);
          await appendAnchorLog(anchorId, `检查录制目录时出错: ${err.message}`);
        }
      }
      
      // 确保数据库状态正确
      if (isRecording === true) {
        await updateAnchorStatus(anchorId, { is_recording: false });
        await updateAnchorTag(anchorId, 'remove');
      }
    }
    
    log(LOG_LEVEL.INFO, `[handleAnchorStreamEnd] 主播 ${anchorId} (${anchorName}) 直播结束处理完成`);
    await appendAnchorLog(anchorId, `直播结束处理完成`);
    
  } catch (err) {
    logError(`[handleAnchorStreamEnd] 处理主播 ${anchorId} 直播结束时出错`, err, anchorId);
    await appendAnchorLog(anchorId, `处理直播结束时出错: ${err.message}`);
    
    // 确保清理状态
    if (activeRecordings.has(anchorId)) {
      activeRecordings.delete(anchorId);
    }
    
    // 确保数据库状态正确
    try {
      await updateAnchorStatus(anchorId, { is_recording: false });
      await updateAnchorTag(anchorId, 'remove');
    } catch (updateErr) {
      logError(`[handleAnchorStreamEnd] 更新数据库状态失败`, updateErr, anchorId);
      await appendAnchorLog(anchorId, `更新数据库状态失败: ${updateErr.message}`);
    }
  }
}

/**
 * 检查录制目录是否有有效文件
 * @param {string} anchorId 主播ID
 * @param {string} subdir 录制目录
 * @returns {boolean} 是否有有效文件
 */
async function checkRecordingDirectory(anchorId, subdir) {
  try {
    const files = await fs.promises.readdir(subdir);
    const validFiles = files.filter(file => {
      if (!file.endsWith('.ts')) return false;
      
      try {
        const filePath = path.join(subdir, file);
        const stat = fs.statSync(filePath);
        return stat.size > 0;
      } catch (err) {
        return false;
      }
    });
    
    log(LOG_LEVEL.INFO, `[checkRecordingDirectory] 主播 ${anchorId} 目录 ${subdir} 中有 ${validFiles.length} 个有效文件`, anchorId);
    return validFiles.length > 0;
  } catch (err) {
    log(LOG_LEVEL.WARN, `[checkRecordingDirectory] 检查录制目录失败: ${subdir}, ${err.message}`, anchorId);
    return false;
  }
}

/**
 * 检测并清理僵尸 ffmpeg 进程
 */
async function detectAndCleanZombieProcesses() {
  try {
    log(LOG_LEVEL.INFO, '[detectAndCleanZombieProcesses] 开始检测僵尸进程');
    
    // 获取所有 ffmpeg 进程
    const { stdout: psOutput } = await execPromise('ps aux | grep ffmpeg | grep -v grep');
    const lines = psOutput.trim().split('\n').filter(line => line.length > 0);
    
    let zombieCount = 0;
    let cleanedCount = 0;
    
    for (const line of lines) {
      const parts = line.split(/\s+/);
      if (parts.length < 11) continue;
      
      const pid = parts[1];
      const stat = parts[7]; // 进程状态
      const cmd = parts.slice(10).join(' ');
      
      // 检查是否是僵尸进程 (Z 状态)
      if (stat.includes('Z')) {
        log(LOG_LEVEL.WARN, `发现僵尸进程 PID: ${pid}, 命令: ${cmd}`);
        zombieCount++;
        
        try {
          // 尝试清理僵尸进程的父进程
          const { stdout: ppidOutput } = await execPromise(`ps -o ppid= -p ${pid}`);
          const ppid = ppidOutput.trim();
          
          if (ppid && ppid !== '1') {
            // 向父进程发送 SIGCHLD 信号，让它回收子进程
            await execPromise(`kill -CHLD ${ppid}`);
            log(LOG_LEVEL.INFO, `已向父进程 ${ppid} 发送 SIGCHLD 信号`);
            cleanedCount++;
          }
        } catch (err) {
          log(LOG_LEVEL.WARN, `清理僵尸进程 ${pid} 失败: ${err.message}`);
        }
      }
      
      // 检查长时间运行的 ffmpeg 进程
      // 从命令行中提取录制的文件路径
      const fileMatch = cmd.match(/recordings\/([^\/]+)\/\d+-\1\.ts/);
      if (fileMatch) {
        const anchorId = fileMatch[1];
        
        // 检查这个进程是否还在 activeRecordings 中
        if (!activeRecordings.has(anchorId)) {
          log(LOG_LEVEL.WARN, `发现孤立的 ffmpeg 进程 PID: ${pid}, anchor_id: ${anchorId}`);
          
          try {
            // 先发送 SIGTERM
            await execPromise(`kill -TERM ${pid}`);
            log(LOG_LEVEL.INFO, `已向孤立进程 ${pid} 发送 SIGTERM 信号`);
            
            // 等待 3 秒
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检查进程是否还存在
            try {
              await execPromise(`kill -0 ${pid}`);
              // 如果还存在，发送 SIGKILL
              await execPromise(`kill -KILL ${pid}`);
              log(LOG_LEVEL.WARN, `强制杀死孤立进程 ${pid}`);
            } catch (e) {
              // kill -0 失败说明进程已经不存在
              log(LOG_LEVEL.INFO, `孤立进程 ${pid} 已成功终止`);
            }
            
            cleanedCount++;
          } catch (err) {
            log(LOG_LEVEL.ERROR, `终止孤立进程 ${pid} 失败: ${err.message}`);
          }
        }
      }
    }
    
    if (zombieCount > 0 || cleanedCount > 0) {
      log(LOG_LEVEL.INFO, `[detectAndCleanZombieProcesses] 检测到 ${zombieCount} 个僵尸进程，清理了 ${cleanedCount} 个进程`);
    } else {
      log(LOG_LEVEL.INFO, '[detectAndCleanZombieProcesses] 未发现僵尸进程或孤立进程');
    }
    
  } catch (err) {
    // ps 命令没有找到 ffmpeg 进程时会返回错误，这是正常的
    if (!err.message.includes('ps aux')) {
      log(LOG_LEVEL.ERROR, `[detectAndCleanZombieProcesses] 检测进程时出错: ${err.message}`);
    }
  }
}

/**
 * 获取文件处理锁
 * @param {string} anchorId 主播ID
 * @param {string} subdir 目录路径
 * @returns {object|null} 锁对象，包含 release 方法
 */
async function acquireProcessingLock(anchorId, subdir) {
  const lockKey = `${anchorId}:${subdir}`;
  const lockInfo = {
    pid: process.pid,
    timestamp: Date.now(),
    anchorId: anchorId
  };
  
  // 检查是否已经有锁
  if (processingLocks.has(lockKey)) {
    const existingLock = processingLocks.get(lockKey);
    const lockAge = Date.now() - existingLock.timestamp;
    
    // 如果锁超过5分钟，认为是过期锁
    if (lockAge > 5 * 60 * 1000) {
      log(LOG_LEVEL.WARN, `发现过期锁（${lockAge/1000}秒），强制解锁`, anchorId);
      processingLocks.delete(lockKey);
    } else {
      log(LOG_LEVEL.INFO, `主播 ${anchorId} 正在被进程 ${existingLock.pid} 处理中`, anchorId);
      return null;
    }
  }
  
  // 设置新锁
  processingLocks.set(lockKey, lockInfo);
  
  // 返回释放锁的方法
  return {
    release: async () => {
      processingLocks.delete(lockKey);
      log(LOG_LEVEL.DEBUG, `释放内存锁: ${lockKey}`, anchorId);
    }
  };
}

/**
 * 处理录制完成的文件 - 拼接并发送到数据库
 * @param {string} anchorId 主播ID
 * @param {object} recordingInfo 录制信息
 * @param {boolean} forceProcessAll 是否强制处理所有文件（用于主播下播时）
 */
async function processRecordedFiles(anchorId, recordingInfo, forceProcessAll = false) {
  const { subdir, anchorInfo, latestOutputFile } = recordingInfo;
  const MAX_FILE_SIZE_BYTES = 1950 * 1024 * 1024; // 1.95GB in bytes
  const processingStartTime = Date.now();
  
  // 记录开始处理文件
  await appendAnchorLog(anchorId, `开始处理录制文件，目录: ${subdir}, 强制处理: ${forceProcessAll}`);
  
  
  // 获取处理锁
  const lock = await acquireProcessingLock(anchorId, subdir);
  if (!lock) {
    log(LOG_LEVEL.INFO, `无法获取主播 ${anchorId} 的处理锁，跳过处理`, anchorId);
    await appendAnchorLog(anchorId, `无法获取处理锁，跳过处理`);
    return;
  }
  
  // 将 fileInfos 声明在 try 块外部，以便在 catch 块中访问
  let fileInfos = [];
  
  try {
    // 1. 扫描主播文件夹，获取所有.ts文件
    const anchorDir = path.join(subdir);
    let allFiles = [];
    
    try {
      allFiles = await fs.promises.readdir(anchorDir);
    } catch (err) {
      log(LOG_LEVEL.WARN, `无法读取主播文件夹: ${anchorDir}, ${err.message}`, anchorId);
      await appendAnchorLog(anchorId, `无法读取主播文件夹: ${err.message}`);
      await lock.release();
      return;
    }
    
    // 2. 过滤.ts文件并获取文件信息，排除正在录制的文件
    fileInfos = []; // 使用外部声明的变量
    for (const fileName of allFiles) {
      if (fileName.endsWith('.ts')) {
        const filePath = path.join(anchorDir, fileName);
        
        
        try {
          const stat = await fs.promises.stat(filePath);
          if (stat.size > 0) {
            const lastModified = stat.mtimeMs;
            
            // 优先从文件名提取时间戳，备用文件创建时间
            const match = fileName.match(/^(\d+)-/);
            const timestamp = match ? parseInt(match[1]) : stat.birthtimeMs;
            fileInfos.push({
              path: filePath,
              size: stat.size,
              timestamp: timestamp,
              fileName: fileName,
              lastModified: lastModified
            });
          } else {
            // 删除空文件
            try {
              await fs.promises.unlink(filePath);
              log(LOG_LEVEL.INFO, `删除空文件: ${fileName}`, anchorId);
              await appendAnchorLog(anchorId, `删除空文件: ${fileName}`);
            } catch (unlinkErr) {
              log(LOG_LEVEL.WARN, `删除空文件失败: ${fileName}, ${unlinkErr.message}`, anchorId);
            }
          }
        } catch (err) {
          log(LOG_LEVEL.WARN, `无法获取文件信息: ${filePath}, ${err.message}`, anchorId);
        }
      }
    }
    
    // 按时间戳排序，确保拼接顺序正确
    fileInfos.sort((a, b) => a.timestamp - b.timestamp);
    
    log(LOG_LEVEL.INFO, `主播 ${anchorId} 文件夹扫描完成，发现 ${fileInfos.length} 个有效文件`, anchorId);
    await appendAnchorLog(anchorId, `扫描完成，发现 ${fileInfos.length} 个有效文件`);
    
    if (fileInfos.length === 0) {
      log(LOG_LEVEL.WARN, `主播 ${anchorId} 没有有效的录制文件`, anchorId);
      await appendAnchorLog(anchorId, `没有有效的录制文件`);
      await lock.release();
      return;
    }
    
    // 特殊处理：如果只有一个文件，直接发送到数据库，无需分组和拼接
    if (fileInfos.length === 1) {
      const singleFile = fileInfos[0];
      log(LOG_LEVEL.INFO, `主播 ${anchorId} 只有一个录制文件，直接发送到数据库: ${singleFile.fileName} (${singleFile.size} bytes)`, anchorId);
      await appendAnchorLog(anchorId, `只有一个录制文件: ${singleFile.fileName} (${(singleFile.size / (1024 * 1024)).toFixed(2)} MB)，直接发送`);
      
      try {
        await sendFileToDatabase(anchorId, singleFile.path, singleFile.size, recordingInfo);
      } catch (err) {
        logError(`发送单个文件到数据库失败: ${singleFile.path}`, err, anchorId);
        await appendAnchorLog(anchorId, `发送单个文件到数据库失败: ${err.message}`);
      }
      
      log(LOG_LEVEL.INFO, `主播 ${anchorId} 的录制文件处理完成 (单文件)`, anchorId);
      await lock.release();
      return;
    }
    
    // 2. 尝试将 ts 文件转换为 mp4
    log(LOG_LEVEL.INFO, `开始尝试将 ${fileInfos.length} 个 ts 文件转换为 mp4`, anchorId);
    await appendAnchorLog(anchorId, `开始尝试将 ${fileInfos.length} 个 ts 文件转换为 mp4`);
    
    // 保存转换成功的文件信息，用于可能的回滚
    const successfullyConvertedFiles = [];
    let convertedCount = 0;
    let failedCount = 0;
    let hasAnyConversionFailed = false;
    
    // 先尝试转换所有文件，但不删除原 TS 文件
    for (const fileInfo of fileInfos) {
      try {
        // 生成 mp4 文件名
        const tsPath = fileInfo.path;
        const mp4Path = tsPath.replace(/\.ts$/, '.mp4');
        const tsBasename = path.basename(tsPath);
        const mp4Basename = path.basename(mp4Path);
        
        log(LOG_LEVEL.INFO, `尝试转换: ${tsBasename} -> ${mp4Basename}`, anchorId);
        await appendAnchorLog(anchorId, `尝试转换: ${tsBasename} -> ${mp4Basename}`);
        
        // 使用 ffmpeg 进行简单的封装转换（不重新编码）
        const convertCmd = `ffmpeg -i "${tsPath}" -c copy -movflags +faststart "${mp4Path}"`;
        await appendAnchorLog(anchorId, `开始执行转码命令: ${tsBasename} -> ${mp4Basename}`);
        
        try {
          const startTime = Date.now();
          await execPromise(convertCmd, { timeout: 60000 }); // 60秒超时
          const duration = ((Date.now() - startTime) / 1000).toFixed(2);
          await appendAnchorLog(anchorId, `转码命令执行完成，耗时: ${duration}秒`);
          
          // 验证转换后的文件
          const mp4Stat = await fs.promises.stat(mp4Path);
          if (mp4Stat.size > 0) {
            // 转换成功，但暂不删除原 TS 文件
            successfullyConvertedFiles.push({
              originalFile: fileInfo,
              mp4Path: mp4Path,
              mp4Info: {
                ...fileInfo,
                path: mp4Path,
                fileName: mp4Basename,
                size: mp4Stat.size
              }
            });
            
            convertedCount++;
            log(LOG_LEVEL.INFO, `转换成功: ${mp4Basename} (${(mp4Stat.size / (1024 * 1024)).toFixed(2)} MB)`, anchorId);
            await appendAnchorLog(anchorId, `转换成功: ${mp4Basename}`);
          } else {
            throw new Error('转换后的文件大小为0');
          }
        } catch (convertErr) {
          // 转换失败
          failedCount++;
          hasAnyConversionFailed = true;
          log(LOG_LEVEL.WARN, `转换失败: ${tsBasename}, 错误: ${convertErr.message}`, anchorId);
          await appendAnchorLog(anchorId, `转换失败: ${tsBasename}`);
          
          // 清理可能存在的失败的 mp4 文件
          try {
            await fs.promises.unlink(mp4Path);
          } catch (e) {
            // 忽略删除错误
          }
        }
      } catch (err) {
        // 处理文件时出错
        failedCount++;
        hasAnyConversionFailed = true;
        log(LOG_LEVEL.ERROR, `处理文件时出错: ${fileInfo.fileName}, 错误: ${err.message}`, anchorId);
        await appendAnchorLog(anchorId, `处理文件时出错: ${fileInfo.fileName}`);
      }
    }
    
    log(LOG_LEVEL.INFO, `转换完成 - 成功: ${convertedCount}, 失败: ${failedCount}, 总计: ${fileInfos.length}`, anchorId);
    
    // 判断是否所有文件都转换成功
    let finalFileInfos = [];
    
    if (hasAnyConversionFailed || failedCount > 0) {
      // 有转换失败，回滚所有 MP4 文件，使用原始 TS 文件
      log(LOG_LEVEL.WARN, `检测到转换失败，将删除所有已转换的 MP4 文件，使用原始 TS 文件`, anchorId);
      await appendAnchorLog(anchorId, `检测到转换失败，将删除所有已转换的 MP4 文件，使用原始 TS 文件`);
      
      // 删除所有已转换的 MP4 文件
      for (const converted of successfullyConvertedFiles) {
        try {
          await fs.promises.unlink(converted.mp4Path);
          log(LOG_LEVEL.INFO, `已删除 MP4 文件: ${path.basename(converted.mp4Path)}`, anchorId);
        } catch (e) {
          log(LOG_LEVEL.WARN, `删除 MP4 文件失败: ${converted.mp4Path}, 错误: ${e.message}`, anchorId);
        }
      }
      
      // 使用原始 TS 文件列表
      finalFileInfos = fileInfos;
      await appendAnchorLog(anchorId, `所有文件将保持 TS 格式`);
    } else {
      // 所有文件都转换成功，删除原始 TS 文件，使用 MP4
      log(LOG_LEVEL.INFO, `所有文件转换成功，删除原始 TS 文件`, anchorId);
      await appendAnchorLog(anchorId, `所有文件转换成功，删除原始 TS 文件`);
      
      for (const converted of successfullyConvertedFiles) {
        try {
          // 删除原始 TS 文件
          await fs.promises.unlink(converted.originalFile.path);
          log(LOG_LEVEL.INFO, `已删除原始 TS 文件: ${converted.originalFile.fileName}`, anchorId);
          // 使用 MP4 文件信息
          finalFileInfos.push(converted.mp4Info);
        } catch (e) {
          log(LOG_LEVEL.WARN, `删除原始 TS 文件失败: ${converted.originalFile.path}, 错误: ${e.message}`, anchorId);
          // 如果删除 TS 失败，仍然使用 MP4
          finalFileInfos.push(converted.mp4Info);
        }
      }
      await appendAnchorLog(anchorId, `所有文件已转换为 MP4 格式`);
    }
    
    // 使用最终的文件列表进行分组
    fileInfos = finalFileInfos;
    
    // 3. 多文件情况：使用贪心算法分组文件，确保每组不超过1.95GB
    const groups = [];
    let currentGroup = [];
    let currentGroupSize = 0;
    
    // 打印所有文件信息用于调试
    log(LOG_LEVEL.INFO, `开始分组处理 ${fileInfos.length} 个文件:`, anchorId);
    await appendAnchorLog(anchorId, `开始分组处理 ${fileInfos.length} 个文件`);
    fileInfos.forEach((file, index) => {
      log(LOG_LEVEL.DEBUG, `  文件${index + 1}: ${file.fileName}, 大小: ${(file.size / (1024 * 1024)).toFixed(2)} MB`, anchorId);
    });
    
    for (const fileInfo of fileInfos) {
      if (currentGroupSize + fileInfo.size > MAX_FILE_SIZE_BYTES && currentGroup.length > 0) {
        // 当前组已满，保存并开始新组
        log(LOG_LEVEL.DEBUG, `当前组已满 (${(currentGroupSize / (1024 * 1024)).toFixed(2)} MB)，包含 ${currentGroup.length} 个文件`, anchorId);
        groups.push({
          files: [...currentGroup],
          totalSize: currentGroupSize
        });
        currentGroup = [fileInfo];
        currentGroupSize = fileInfo.size;
        log(LOG_LEVEL.DEBUG, `开始新组，首个文件: ${fileInfo.fileName} (${(fileInfo.size / (1024 * 1024)).toFixed(2)} MB)`, anchorId);
      } else {
        // 添加到当前组
        currentGroup.push(fileInfo);
        currentGroupSize += fileInfo.size;
        log(LOG_LEVEL.DEBUG, `添加文件到当前组: ${fileInfo.fileName} (${(fileInfo.size / (1024 * 1024)).toFixed(2)} MB)，组大小: ${(currentGroupSize / (1024 * 1024)).toFixed(2)} MB`, anchorId);
      }
    }
    
    // 添加最后一组
    if (currentGroup.length > 0) {
      log(LOG_LEVEL.DEBUG, `添加最后一组，包含 ${currentGroup.length} 个文件，总大小: ${(currentGroupSize / (1024 * 1024)).toFixed(2)} MB`, anchorId);
      groups.push({
        files: currentGroup,
        totalSize: currentGroupSize
      });
    }
    
    // 打印分组结果
    log(LOG_LEVEL.INFO, `分组完成，共 ${groups.length} 组:`, anchorId);
    await appendAnchorLog(anchorId, `分组完成，共 ${groups.length} 组`);
    
    groups.forEach((group, index) => {
      const groupMsg = `组${index + 1}: ${group.files.length} 个文件，总大小: ${(group.totalSize / (1024 * 1024)).toFixed(2)} MB`;
      log(LOG_LEVEL.INFO, `  ${groupMsg}`, anchorId);
    });
    
    log(LOG_LEVEL.INFO, `文件将被拼接为 ${groups.length} 个最终文件`, anchorId);
    await appendAnchorLog(anchorId, `文件将被拼接为 ${groups.length} 个最终文件`);
    
    // 3. 处理每个组
    const finalFiles = [];
    const originalFiles = [...fileInfos]; // 保存原始文件列表作为备份
    
    // 创建处理子文件夹
    const processingDir = path.join(subdir, 'processing_' + Date.now());
    try {
      await fs.promises.mkdir(processingDir, { recursive: true });
      log(LOG_LEVEL.INFO, `创建处理子文件夹: ${path.basename(processingDir)}`, anchorId);
      await appendAnchorLog(anchorId, `创建处理子文件夹: ${path.basename(processingDir)}`);
    } catch (err) {
      logError(`创建处理子文件夹失败`, err, anchorId);
      await appendAnchorLog(anchorId, `创建处理子文件夹失败: ${err.message}`);
      throw err;
    }
    
    log(LOG_LEVEL.INFO, `开始处理 ${groups.length} 个文件组`, anchorId);
    await appendAnchorLog(anchorId, `开始处理 ${groups.length} 个文件组`);
    
    let processedGroups = 0;
    let hasAnyFailure = false;
    const processedFiles = []; // 记录所有成功处理的文件
    
    try {
      for (let groupIndex = 0; groupIndex < groups.length; groupIndex++) {
      const group = groups[groupIndex];
      log(LOG_LEVEL.INFO, `开始处理第 ${groupIndex + 1}/${groups.length} 组，包含 ${group.files.length} 个文件，总大小: ${(group.totalSize / (1024 * 1024)).toFixed(2)} MB`, anchorId);
      await appendAnchorLog(anchorId, `开始处理第 ${groupIndex + 1}/${groups.length} 组，包含 ${group.files.length} 个文件`);
      
      // 使用每个分组第一个文件的时间戳作为该分组合并后文件的时间戳
      const groupTimestamp = group.files[0].timestamp;
      // 检查第一个文件的扩展名来决定输出文件的扩展名
      const firstFileExt = path.extname(group.files[0].path);
      const outputExt = firstFileExt === '.mp4' ? '.mp4' : '.ts';
      const groupOutputFile = path.join(processingDir, `${groupTimestamp}-${anchorId}${outputExt}`); // 输出到处理子文件夹
      
      try {
        if (group.files.length === 1) {
          // 单个文件，复制到处理文件夹
          const sourceFile = group.files[0].path;
          const sourceBasename = path.basename(sourceFile);
          const targetBasename = path.basename(groupOutputFile);
          
          log(LOG_LEVEL.DEBUG, `准备复制文件: ${sourceBasename} -> ${targetBasename}`, anchorId);
          await appendAnchorLog(anchorId, `复制文件: ${sourceBasename} -> 处理文件夹`);
          
          try {
            // 使用复制而不是重命名，这样原文件保持不变
            await fs.promises.copyFile(sourceFile, groupOutputFile);
            log(LOG_LEVEL.INFO, `组 ${groupIndex + 1}: 单个文件，复制成功: ${sourceBasename} -> ${targetBasename}`, anchorId);
            await appendAnchorLog(anchorId, `组 ${groupIndex + 1}: 单个文件，复制成功`);
          } catch (copyErr) {
            log(LOG_LEVEL.ERROR, `复制文件失败: ${sourceFile} -> ${groupOutputFile}, 错误: ${copyErr.message}`, anchorId);
            await appendAnchorLog(anchorId, `错误：复制文件失败: ${copyErr.message}`);
            throw copyErr; // 重新抛出错误，让外层catch处理
          }
          
          // 验证输出文件并添加到 finalFiles
          try {
            const outputStat = await fs.promises.stat(groupOutputFile);
            if (outputStat.size > 0) {
              // 检查文件大小是否超限
              if (outputStat.size > MAX_FILE_SIZE_BYTES) {
                log(LOG_LEVEL.ERROR, `单个文件超过大小限制: ${(outputStat.size / (1024 * 1024)).toFixed(2)} MB`, anchorId);
                await appendAnchorLog(anchorId, `错误：单个文件超过大小限制: ${(outputStat.size / (1024 * 1024)).toFixed(2)} MB`);
                // 单个文件超限是异常情况，记录错误但仍然保留
              }
              
              processedFiles.push({
                path: groupOutputFile,
                size: outputStat.size,
                groupIndex: groupIndex,
                originalFiles: group.files
              });
              log(LOG_LEVEL.DEBUG, `文件验证成功，已添加到处理列表: ${path.basename(groupOutputFile)}`, anchorId);
            } else {
              log(LOG_LEVEL.WARN, `重命名后的文件大小为0: ${groupOutputFile}`, anchorId);
              await appendAnchorLog(anchorId, `警告：重命名后的文件大小为0: ${path.basename(groupOutputFile)}`);
            }
          } catch (statErr) {
            log(LOG_LEVEL.ERROR, `无法验证重命名后的文件: ${groupOutputFile}, 错误: ${statErr.message}`, anchorId);
            await appendAnchorLog(anchorId, `错误：无法验证重命名后的文件: ${path.basename(groupOutputFile)}, ${statErr.message}`);
            // 验证失败，抛出错误
            throw statErr;
          }
        } else {
          // 多个文件，需要合并
          
          // 先检测所有文件的分辨率，找出最高画质
          let maxWidth = 0;
          let maxHeight = 0;
          let maxBitrate = 0;
          let hasMultipleResolutions = false;
          const fileResolutions = [];
          
          try {
            log(LOG_LEVEL.INFO, `开始检测 ${group.files.length} 个文件的分辨率`, anchorId);
            await appendAnchorLog(anchorId, `开始检测 ${group.files.length} 个文件的分辨率`);
            
            for (const fileInfo of group.files) {
            try {
              const probeCmd = `ffprobe -v error -select_streams v:0 -show_entries stream=width,height,bit_rate -of json "${fileInfo.path}"`;
              const { stdout } = await execPromise(probeCmd, { timeout: 10000 }); // 添加10秒超时
              const probeResult = JSON.parse(stdout);
              
              if (probeResult.streams && probeResult.streams[0]) {
                const stream = probeResult.streams[0];
                const width = parseInt(stream.width) || 0;
                const height = parseInt(stream.height) || 0;
                const bitrate = parseInt(stream.bit_rate) || 0;
                
                fileResolutions.push({ file: fileInfo.path, width, height, bitrate });
                
                if (width > maxWidth || height > maxHeight) {
                  maxWidth = Math.max(width, maxWidth);
                  maxHeight = Math.max(height, maxHeight);
                }
                maxBitrate = Math.max(bitrate, maxBitrate);
              }
            } catch (err) {
              log(LOG_LEVEL.WARN, `检测文件分辨率失败: ${fileInfo.path}, 错误: ${err.message}`, anchorId);
              await appendAnchorLog(anchorId, `检测文件分辨率失败: ${path.basename(fileInfo.path)}, 错误: ${err.message}`);
              // 如果检测失败，继续处理，使用默认值
            }
          }
          
          // 检查是否有多种分辨率
          log(LOG_LEVEL.INFO, `分辨率检测完成，检测到 ${fileResolutions.length} 个文件的分辨率信息`, anchorId);
          await appendAnchorLog(anchorId, `分辨率检测完成，检测到 ${fileResolutions.length} 个文件的分辨率信息`);
          
          if (fileResolutions.length > 1) {
            const firstRes = fileResolutions[0];
            hasMultipleResolutions = fileResolutions.some(r => 
              r.width !== firstRes.width || r.height !== firstRes.height
            );
          }
          
          // 生成输出文件名
          const outputFileName = path.basename(groupOutputFile);
          
          // 现在只有两种情况：全部是 MP4 或全部是 TS
          const firstFileExt = path.extname(group.files[0].path);
          const isAllMp4 = firstFileExt === '.mp4';
          
          log(LOG_LEVEL.INFO, `分组文件格式: ${isAllMp4 ? 'MP4' : 'TS'}`, anchorId);
          
          let mergeCmd;
          
          try {
          
          if (isAllMp4) {
            // MP4 文件需要使用 concat demuxer
            log(LOG_LEVEL.INFO, `检测到所有文件都是 MP4 格式，使用 concat demuxer`, anchorId);
            await appendAnchorLog(anchorId, `检测到所有文件都是 MP4 格式，使用 concat demuxer`);
            
            // 创建临时文件列表
            const listFileName = `concat_list_${groupTimestamp}_${anchorId}.txt`;
            const listFilePath = path.join(processingDir, listFileName);
            const fileListContent = group.files.map((f) => {
              return `file '${f.path}'`; // 使用完整路径
            }).join('\n');
            
            await fs.promises.writeFile(listFilePath, fileListContent);
            
            if (hasMultipleResolutions && maxWidth > 0 && maxHeight > 0) {
              // 如果有多种分辨率，使用最高分辨率重新编码
              log(LOG_LEVEL.INFO, `检测到多种分辨率，将统一转换为 ${maxWidth}x${maxHeight}`, anchorId);
              await appendAnchorLog(anchorId, `检测到多种分辨率，将统一转换为 ${maxWidth}x${maxHeight}`);
              
              mergeCmd = `ffmpeg -f concat -safe 0 -i "${listFilePath}" ` +
                        `-vf "scale=${maxWidth}:${maxHeight}:force_original_aspect_ratio=decrease,pad=${maxWidth}:${maxHeight}:(ow-iw)/2:(oh-ih)/2" ` +
                        `-c:v libx264 -preset fast -crf 23 ` +
                        `-c:a aac -b:a 128k ` +
                        `-movflags +faststart "${groupOutputFile}"`;
            } else {
              // 分辨率一致，直接复制流
              log(LOG_LEVEL.INFO, `所有文件分辨率一致，使用流复制模式`, anchorId);
              mergeCmd = `ffmpeg -f concat -safe 0 -i "${listFilePath}" -c copy -movflags +faststart "${groupOutputFile}"`;
            }
            
            // 合并后清理临时文件列表
            try {
              await execPromise(mergeCmd, { cwd: subdir, timeout: 300000 });
              await fs.promises.unlink(listFilePath);
            } catch (err) {
              await fs.promises.unlink(listFilePath);
              throw err;
            }
          } else {
            // TS 文件使用 concat 协议
            const concatInput = group.files.map((f) => {
              return f.path; // 使用完整路径
            }).join('|');
            
            if (hasMultipleResolutions && maxWidth > 0 && maxHeight > 0) {
              // 如果有多种分辨率，使用最高分辨率重新编码
              log(LOG_LEVEL.INFO, `检测到多种分辨率，将统一转换为 ${maxWidth}x${maxHeight}`, anchorId);
              await appendAnchorLog(anchorId, `检测到多种分辨率，将统一转换为 ${maxWidth}x${maxHeight}`);
              
              // 使用 concat 协议并重新编码到最高分辨率
              mergeCmd = `ffmpeg -i "concat:${concatInput}" ` +
                        `-vf "scale=${maxWidth}:${maxHeight}:force_original_aspect_ratio=decrease,pad=${maxWidth}:${maxHeight}:(ow-iw)/2:(oh-ih)/2" ` +
                        `-c:v libx264 -preset fast -crf 23 ` +
                        `-c:a aac -b:a 128k ` +
                        `-f mpegts "${groupOutputFile}"`;
            } else {
              // 分辨率一致，直接复制流
              log(LOG_LEVEL.INFO, `所有文件分辨率一致，使用流复制模式`, anchorId);
              mergeCmd = `ffmpeg -i "concat:${concatInput}" -c copy -f mpegts "${groupOutputFile}"`;
            }
            
            await execPromise(mergeCmd, { cwd: subdir, timeout: 300000 });
          }
          
          log(LOG_LEVEL.INFO, `ffmpeg 合并命令执行成功`, anchorId);
          await appendAnchorLog(anchorId, `ffmpeg 合并命令执行成功`);
        } catch (ffmpegErr) {
            // 提取 ffmpeg 的错误输出
            const errorOutput = ffmpegErr.stderr || ffmpegErr.message || '未知错误';
            log(LOG_LEVEL.ERROR, `FFmpeg 合并命令失败: ${mergeCmd}`, anchorId);
            log(LOG_LEVEL.ERROR, `FFmpeg 错误输出: ${errorOutput}`, anchorId);
            throw ffmpegErr; // 重新抛出以便上层处理
          }
          
          log(LOG_LEVEL.INFO, `组 ${groupIndex + 1}: 合并 ${group.files.length} 个文件 (总大小: ${group.totalSize} bytes) 为 ${groupOutputFile}`, anchorId);
          await appendAnchorLog(anchorId, `组 ${groupIndex + 1}: 合并 ${group.files.length} 个文件 (总大小: ${(group.totalSize / (1024 * 1024)).toFixed(2)} MB)`);
          
          // 先检查合并后的文件大小
          let mergedFileStat;
          try {
            mergedFileStat = await fs.promises.stat(groupOutputFile);
            const mergedFileSizeMB = mergedFileStat.size / (1024 * 1024);
            log(LOG_LEVEL.INFO, `合并后文件大小: ${mergedFileSizeMB.toFixed(2)} MB`, anchorId);
            await appendAnchorLog(anchorId, `合并后文件大小: ${mergedFileSizeMB.toFixed(2)} MB`);
            
            // 检查是否超过大小限制
            if (mergedFileStat.size > MAX_FILE_SIZE_BYTES) {
              log(LOG_LEVEL.ERROR, `合并后文件超过大小限制 (${mergedFileSizeMB.toFixed(2)} MB > ${(MAX_FILE_SIZE_BYTES / (1024 * 1024)).toFixed(2)} MB)，放弃合并`, anchorId);
              await appendAnchorLog(anchorId, `错误：合并后文件超过大小限制，放弃合并`);
              
              // 删除超大的合并文件
              try {
                await fs.promises.unlink(groupOutputFile);
                log(LOG_LEVEL.INFO, `已删除超大的合并文件: ${path.basename(groupOutputFile)}`, anchorId);
              } catch (delErr) {
                log(LOG_LEVEL.ERROR, `删除超大合并文件失败: ${delErr.message}`, anchorId);
              }
              
              
              // 抛出错误，让外层处理逻辑处理原始文件
              throw new Error(`合并后文件超过大小限制: ${mergedFileSizeMB.toFixed(2)} MB`);
            }
            
            // 文件大小合格，添加到处理成功列表
            log(LOG_LEVEL.INFO, `合并后文件大小合格: ${mergedFileSizeMB.toFixed(2)} MB`, anchorId);
            await appendAnchorLog(anchorId, `合并后文件验证通过`);
            
            // 添加到成功列表
            processedFiles.push({
              path: groupOutputFile,
              size: mergedFileStat.size,
              groupIndex: groupIndex,
              originalFiles: group.files
            });
          } catch (statErr) {
            log(LOG_LEVEL.ERROR, `无法获取合并后文件信息: ${statErr.message}`, anchorId);
            await appendAnchorLog(anchorId, `错误：无法获取合并后文件信息`);
            
            throw statErr;
          }
        } catch (resolutionErr) {
            // 分辨率检测失败，重新抛出错误
            log(LOG_LEVEL.ERROR, `分辨率检测过程失败: ${resolutionErr.message}`, anchorId);
            await appendAnchorLog(anchorId, `分辨率检测过程失败: ${resolutionErr.message}`);
            throw resolutionErr;
          }
        }
        
        // 对于已经在上面添加到processedFiles的情况，这里只记录日志
        log(LOG_LEVEL.INFO, `组 ${groupIndex + 1} 处理成功`, anchorId);
      } catch (err) {
        logError(`处理组 ${groupIndex + 1} 时出错`, err, anchorId);
        await appendAnchorLog(anchorId, `处理组 ${groupIndex + 1} 时出错: ${err.message}`);
        log(LOG_LEVEL.ERROR, `错误详情 - 组内文件数: ${group.files.length}, 文件列表: ${group.files.map(f => f.fileName).join(', ')}`, anchorId);
        await appendAnchorLog(anchorId, `合并失败的文件列表: ${group.files.map(f => f.fileName).join(', ')}`);
        log(LOG_LEVEL.ERROR, `目标输出文件: ${groupOutputFile}`, anchorId);
        log(LOG_LEVEL.ERROR, `组文件总大小: ${group.totalSize} bytes`, anchorId);
        
        // 标记有失败，停止处理
        hasAnyFailure = true;
        log(LOG_LEVEL.WARN, `组 ${groupIndex + 1} 处理失败，将回滚所有处理`, anchorId);
        await appendAnchorLog(anchorId, `组 ${groupIndex + 1} 处理失败，停止处理并准备回滚`);
        break; // 跳出循环
      }
      
      // 添加明确的进度日志
      log(LOG_LEVEL.INFO, `=== 组 ${groupIndex + 1}/${groups.length} 处理完成 ===`, anchorId);
      await appendAnchorLog(anchorId, `=== 组 ${groupIndex + 1}/${groups.length} 处理完成，当前处理成功文件数: ${processedFiles.length} ===`);
      
      // 如果这是最后一个组，额外记录
      if (groupIndex === groups.length - 1) {
        log(LOG_LEVEL.INFO, `*** 所有 ${groups.length} 个组都已处理完成 ***`, anchorId);
        await appendAnchorLog(anchorId, `*** 所有 ${groups.length} 个组都已处理完成 ***`);
      }
      
      processedGroups++;
    }
    } catch (loopErr) {
      log(LOG_LEVEL.ERROR, `处理文件组时发生错误，已处理 ${processedGroups}/${groups.length} 个组`, anchorId);
      await appendAnchorLog(anchorId, `处理文件组时发生错误：${loopErr.message}，已处理 ${processedGroups}/${groups.length} 个组`);
      log(LOG_LEVEL.ERROR, `错误堆栈: ${loopErr.stack}`, anchorId);
      hasAnyFailure = true;
    }
    
    // 根据处理结果决定使用哪个文件列表
    if (hasAnyFailure) {
      // 有失败，回滚：删除处理文件夹，使用原始文件
      log(LOG_LEVEL.WARN, `检测到处理失败，删除处理文件夹并使用原始文件`, anchorId);
      await appendAnchorLog(anchorId, `检测到处理失败，删除处理文件夹并使用原始文件`);
      
      try {
        await fs.promises.rm(processingDir, { recursive: true, force: true });
        log(LOG_LEVEL.INFO, `已删除处理文件夹: ${path.basename(processingDir)}`, anchorId);
        await appendAnchorLog(anchorId, `已删除处理文件夹`);
      } catch (err) {
        log(LOG_LEVEL.ERROR, `删除处理文件夹失败: ${err.message}`, anchorId);
      }
      
      // 使用原始文件
      for (const fileInfo of originalFiles) {
        finalFiles.push(fileInfo);
      }
      
      log(LOG_LEVEL.INFO, `将使用 ${finalFiles.length} 个原始文件`, anchorId);
      await appendAnchorLog(anchorId, `将使用 ${finalFiles.length} 个原始文件`);
    } else {
      // 全部成功，使用处理后的文件
      log(LOG_LEVEL.INFO, `所有组处理成功，删除原始文件`, anchorId);
      await appendAnchorLog(anchorId, `所有组处理成功，开始删除原始文件`);
      
      // 删除原始文件
      for (const processedFile of processedFiles) {
        // 删除该组的原始文件
        for (const origFile of processedFile.originalFiles) {
          try {
            await fs.promises.unlink(origFile.path);
            log(LOG_LEVEL.DEBUG, `已删除原始文件: ${origFile.fileName}`, anchorId);
          } catch (err) {
            log(LOG_LEVEL.WARN, `删除原始文件失败: ${origFile.fileName}, ${err.message}`, anchorId);
          }
        }
        
        // 将处理后的文件添加到finalFiles
        finalFiles.push({
          path: processedFile.path,
          size: processedFile.size
        });
      }
      
      log(LOG_LEVEL.INFO, `将使用 ${finalFiles.length} 个处理后的文件`, anchorId);
      await appendAnchorLog(anchorId, `将使用 ${finalFiles.length} 个处理后的文件`);
    }
    
    // 4. 发送最终文件到数据库
    log(LOG_LEVEL.INFO, `准备发送 ${finalFiles.length} 个文件到数据库`, anchorId);
    await appendAnchorLog(anchorId, `准备发送 ${finalFiles.length} 个文件到数据库`);
    
    let sentCount = 0;
    let sendErrors = 0;
    
    for (let i = 0; i < finalFiles.length; i++) {
      const fileInfo = finalFiles[i];
      try {
        log(LOG_LEVEL.INFO, `开始发送文件 ${i + 1}/${finalFiles.length}: ${path.basename(fileInfo.path)} (${(fileInfo.size / (1024 * 1024)).toFixed(2)} MB)`, anchorId);
        await appendAnchorLog(anchorId, `开始发送文件 ${i + 1}/${finalFiles.length}: ${path.basename(fileInfo.path)}`);
        
        await sendFileToDatabase(anchorId, fileInfo.path, fileInfo.size, recordingInfo);
        sentCount++;
        
        log(LOG_LEVEL.INFO, `文件 ${i + 1}/${finalFiles.length} 发送成功`, anchorId);
        await appendAnchorLog(anchorId, `文件 ${i + 1}/${finalFiles.length} 发送成功`);
      } catch (err) {
        sendErrors++;
        logError(`发送文件到数据库失败: ${fileInfo.path}`, err, anchorId);
        await appendAnchorLog(anchorId, `发送文件到数据库失败: ${path.basename(fileInfo.path)}, 错误: ${err.message}`);
      }
    }
    
    log(LOG_LEVEL.INFO, `数据库发送完成 - 成功: ${sentCount}, 失败: ${sendErrors}, 总计: ${finalFiles.length}`, anchorId);
    await appendAnchorLog(anchorId, `数据库发送完成 - 成功: ${sentCount}, 失败: ${sendErrors}, 总计: ${finalFiles.length}`);
    
    // 处理状态记录已移除（原Redis功能）
    
    log(LOG_LEVEL.INFO, `主播 ${anchorId} 的所有录制文件处理完成`, anchorId);
    await appendAnchorLog(anchorId, `所有录制文件处理完成`);
    
  } catch (err) {
    logError(`处理主播 ${anchorId} 的录制文件时发生错误`, err, anchorId);
    await appendAnchorLog(anchorId, `处理录制文件时发生错误: ${err.message}`);
    
    // 处理失败状态记录已移除（原Redis功能）
    
    // 在整体错误情况下，尝试发送原始文件
    try {
      if (fileInfos && fileInfos.length > 0) {
        log(LOG_LEVEL.WARN, `由于处理错误，尝试直接发送 ${fileInfos.length} 个原始文件`, anchorId);
        await appendAnchorLog(anchorId, `由于处理错误，尝试直接发送 ${fileInfos.length} 个原始文件`);
        
        for (const fileInfo of fileInfos) {
          try {
            await fs.promises.access(fileInfo.path);
            await sendFileToDatabase(anchorId, fileInfo.path, fileInfo.size, recordingInfo);
            log(LOG_LEVEL.INFO, `错误恢复：成功发送原始文件 ${fileInfo.fileName}`, anchorId);
            await appendAnchorLog(anchorId, `错误恢复：成功发送原始文件 ${fileInfo.fileName}`);
          } catch (sendErr) {
            logError(`错误恢复：发送原始文件失败: ${fileInfo.path}`, sendErr, anchorId);
            await appendAnchorLog(anchorId, `错误恢复：发送原始文件失败: ${fileInfo.fileName}, 错误: ${sendErr.message}`);
          }
        }
      }
    } catch (recoveryErr) {
      logError(`错误恢复过程失败`, recoveryErr, anchorId);
      await appendAnchorLog(anchorId, `错误恢复过程失败: ${recoveryErr.message}`);
    }
  } finally {
    // 确保释放锁
    if (lock) {
      await lock.release();
      log(LOG_LEVEL.DEBUG, `已释放主播 ${anchorId} 的处理锁`, anchorId);
    }
  }
}

/**
 * 在 file_id 表中创建文件记录
 * @param {string} anchorId 主播ID
 * @param {string} filePath 文件路径
 * @param {number} fileSize 文件大小
 * @param {object} recordingInfo 录制信息
 */
async function sendFileToDatabase(anchorId, filePath, fileSize, recordingInfo) {
  const maxRetries = 3;
  const retryDelay = 5000; // 5秒
  const fileName = path.basename(filePath);
  // 确保是绝对路径
  const absolutePath = path.isAbsolute(filePath) ? filePath : path.resolve(filePath);
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // 记录开始发送文件到数据库
      if (attempt === 1) {
        await appendAnchorLog(anchorId, `开始在 file_id 表创建记录: ${fileName} (${(fileSize / (1024 * 1024)).toFixed(2)} MB)`);
      } else {
        await appendAnchorLog(anchorId, `重试创建 file_id 记录 (第${attempt}/${maxRetries}次): ${fileName}`);
      }
      
      // 验证文件名格式：{timestamp}-{anchorId}.ts 或 {timestamp}-{anchorId}.mp4
      const fileNamePattern = /^\d+-[a-f0-9-]+\.(ts|mp4)$/;
      if (!fileNamePattern.test(fileName)) {
        log(LOG_LEVEL.WARN, `文件名格式不符合规范: ${fileName}，但仍将处理`, anchorId);
        if (attempt === 1) {
          await appendAnchorLog(anchorId, `警告：文件名格式不符合规范: ${fileName}`);
        }
      }
      
      // 从文件名中提取timestamp作为recordtime
      // 文件名格式：{timestamp}-{anchorId}.ts 或 {timestamp}-{anchorId}.mp4
      const timestampMatch = fileName.match(/^(\d+)-/);
      let recordTime;
      let isFileNameValid = true;
      
      if (timestampMatch) {
        recordTime = timestampMatch[1];
      } else {
        // 文件名不规范，使用当前时间戳作为recordtime
        recordTime = Math.floor(Date.now() / 1000).toString();
        isFileNameValid = false;
        log(LOG_LEVEL.WARN, `文件名不规范，无法提取时间戳: ${fileName}，使用当前时间戳: ${recordTime}`, anchorId);
        await appendAnchorLog(anchorId, `警告：文件名不规范，使用当前时间戳: ${recordTime}`);
      }
      
      // 准备额外数据
      const additionalData = {};
      if (recordingInfo && recordingInfo.anchorInfo) {
        if (recordingInfo.anchorInfo.title) {
          additionalData.title = recordingInfo.anchorInfo.title;
        }
        if (recordingInfo.anchorInfo.platform) {
          additionalData.platform = recordingInfo.anchorInfo.platform;
        }
        if (recordingInfo.anchorInfo.ordertime) {
          additionalData.ordertime = recordingInfo.anchorInfo.ordertime;
        }
        // tag字段不再从anchors表传递，而是使用PROGRAM_TAG
      }
      
      // 如果文件名不规范，直接在这里创建失败记录
      if (!isFileNameValid) {
        await createFailedFileIdRecord(anchorId, absolutePath, recordTime, '文件名不规范', additionalData);
        log(LOG_LEVEL.INFO, `已为文件名不规范的文件创建失败记录: ${fileName}`, anchorId);
        await appendAnchorLog(anchorId, `已为文件名不规范的文件创建失败记录`);
      } else {
        // 文件名规范，正常创建记录
        await createFileIdRecord(anchorId, absolutePath, recordTime, additionalData);
        log(LOG_LEVEL.INFO, `成功在 file_id 表创建记录: ${absolutePath} (${fileSize} bytes)`, anchorId);
        await appendAnchorLog(anchorId, `成功在 file_id 表创建记录: ${fileName} (${(fileSize / (1024 * 1024)).toFixed(2)} MB)`);
      }
      
      return; // 成功发送，退出函数
      
    } catch (err) {
      logError(`发送文件到数据库失败 (尝试 ${attempt}/${maxRetries}): ${absolutePath}`, err, anchorId);
      await appendAnchorLog(anchorId, `发送文件到数据库失败 (尝试 ${attempt}/${maxRetries}): ${fileName}, 错误: ${err.message}`);
      
      if (attempt === maxRetries) {
        // 最后一次尝试失败，记录详细错误并抛出
        log(LOG_LEVEL.ERROR, `发送文件到数据库彻底失败: ${absolutePath}, 错误堆栈: ${err.stack}`, anchorId);
        await appendAnchorLog(anchorId, `发送文件到数据库彻底失败: ${fileName}，已尝试 ${maxRetries} 次`);
        throw err; // 重新抛出错误，让调用者处理
      } else {
        // 等待后重试
        log(LOG_LEVEL.INFO, `等待 ${retryDelay/1000} 秒后重试...`, anchorId);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }
}

/**
 * 手动触发已完成录制的文件处理（用于修复遗漏的处理）
 * @param {string} anchorId 主播ID
 */
async function manuallyProcessRecordedFiles(anchorId) {
  try {
    log(LOG_LEVEL.INFO, `[manuallyProcessRecordedFiles] 手动触发主播 ${anchorId} 的文件处理`);
    
    // 文件锁机制已经处理了并发问题，不再需要 cleanupDone
    
    // 构建录制目录
    const subdir = path.join('recordings', anchorId);
    
    // 检查目录是否存在
    const hasValidFiles = await checkRecordingDirectory(anchorId, subdir);
    if (!hasValidFiles) {
      log(LOG_LEVEL.WARN, `[manuallyProcessRecordedFiles] 主播 ${anchorId} 没有有效的录制文件`);
      return false;
    }
    
    // 查询主播信息
    const { data: anchorData, error: anchorError } = await supabase
      .from('anchors')
      .select('*')
      .eq('anchor_id', anchorId)
      .single();
      
    if (anchorError || !anchorData) {
      log(LOG_LEVEL.ERROR, `[manuallyProcessRecordedFiles] 查询主播 ${anchorId} 信息失败: ${anchorError?.message}`);
      return false;
    }
    
    // 构建 recordingInfo 对象
    const recordingInfo = {
      subdir: subdir,
      anchorInfo: anchorData,
      latestOutputFile: null // 没有正在录制的文件
    };
    
    // 处理文件
    await processRecordedFiles(anchorId, recordingInfo);
    
    log(LOG_LEVEL.INFO, `[manuallyProcessRecordedFiles] 主播 ${anchorId} 的文件处理已完成`);
    return true;
    
  } catch (err) {
    logError(`[manuallyProcessRecordedFiles] 处理主播 ${anchorId} 时出错`, err, anchorId);
    return false;
  }
}

// 导出函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { manuallyProcessRecordedFiles };
}

/**
 * 辅助函数：尝试发送最后一个文件段到数据库
 * @param {string} anchorId 主播ID
 * @param {string} filePath 文件路径
 */
async function trySendLastSegment(anchorId, filePath, recordingInfo) {
  if (!filePath) {
    log(LOG_LEVEL.DEBUG, `[trySendLastSegment] 文件路径为空，跳过发送 (anchor_id: ${anchorId})`);
    return;
  }
  
  try {
    const stat = await fs.promises.stat(filePath);
    if (stat.size > 0) {
      // 确保是绝对路径
      const absolutePath = path.isAbsolute(filePath) ? filePath : path.resolve(filePath);
      const fileName = path.basename(filePath);
      
      // 从文件名中提取timestamp作为recordtime
      const timestampMatch = fileName.match(/^(\d+)-/);
      if (timestampMatch) {
        const recordTime = timestampMatch[1];
        
        // 准备额外数据
        const additionalData = {};
        if (recordingInfo && recordingInfo.anchorInfo) {
          if (recordingInfo.anchorInfo.title) {
            additionalData.title = recordingInfo.anchorInfo.title;
          }
          if (recordingInfo.anchorInfo.platform) {
            additionalData.platform = recordingInfo.anchorInfo.platform;
          }
          if (recordingInfo.anchorInfo.ordertime) {
            additionalData.ordertime = recordingInfo.anchorInfo.ordertime;
          }
          // tag字段不再从anchors表传递，而是使用PROGRAM_TAG
        }
        
        // 在file_id表中创建记录
        await createFileIdRecord(anchorId, absolutePath, recordTime, additionalData);
        
        log(LOG_LEVEL.INFO, `[trySendLastSegment] 成功在 file_id 表补发记录: ${absolutePath}, anchor_id: ${anchorId}`);
        await appendAnchorLog(anchorId, `成功在 file_id 表补发记录: ${fileName}`);
      } else {
        log(LOG_LEVEL.WARN, `[trySendLastSegment] 无法从文件名提取recordtime: ${fileName}, anchor_id: ${anchorId}`);
      }
    } else {
      log(LOG_LEVEL.WARN, `[trySendLastSegment] 文件为空，不发送: ${filePath}, anchor_id: ${anchorId}`);
    }
  } catch (err) {
    if (err.code === 'ENOENT') {
      log(LOG_LEVEL.INFO, `[trySendLastSegment] 文件未找到，静默失败: ${filePath}, anchor_id: ${anchorId}`);
    } else {
      logError(`[trySendLastSegment] 在 file_id 表创建记录时出错`, err, anchorId);
      await appendAnchorLog(anchorId, `在 file_id 表补发记录失败: ${path.basename(filePath)}, 错误: ${err.message}`);
    }
  }
}

/**
 * 录制流的核心逻辑
 */
async function recordStream(streamUrlObject, anchorId, anchorInfo) {
  // 记录开始录制流
  await appendAnchorLog(anchorId, `开始录制流，主播名: ${anchorInfo.anchor_name}, 平台: ${anchorInfo.platform}`);
  
  // 获取最佳URL
  await appendAnchorLog(anchorId, `[recordStream] 开始从stream_url对象中提取最佳录制URL`);
  let mainUrl = extractBestStreamUrl(streamUrlObject, null, anchorInfo.platform);
  if (!mainUrl) {
    await appendAnchorLog(anchorId, `[recordStream] 错误: 无法从流对象中提取有效URL`);
    throw new Error(`无法从流对象中提取有效URL`);
  }
  
  await appendAnchorLog(anchorId, `[recordStream] 成功提取录制URL: ${mainUrl}`);
  await appendAnchorLog(anchorId, `[recordStream] 这是FFmpeg将要使用的实际录制地址`);
  
  // 存储当前使用的画质级别，用于后续获取相同画质新链接
  let currentQualityLevel = extractQualityFromUrl(mainUrl);
  log(LOG_LEVEL.INFO, `当前选择的画质级别: ${currentQualityLevel || '未知'}`, anchorId);
  await appendAnchorLog(anchorId, `选择画质级别: ${currentQualityLevel || '未知'}, URL: ${mainUrl}`);
  
  // 状态监测相关变量（只在错误时检查）
  let consecutiveFalseStatusCount = 0;
  
  // 添加错误计数和备用链接切换逻辑
  let currentBackupIndex = 0; 
  
  // 文件大小限制 1.95GB
  const MAX_FILE_SIZE_BYTES = 1950 * 1024 * 1024; // 1.95GB in bytes
  
  
  let backupUrl = null;
  const bracketMatch = mainUrl.match(/\[(https?:\/\/[^\]]+)\]$/);
  if (bracketMatch) {
    backupUrl = bracketMatch[1];
    mainUrl = mainUrl.replace(/\[https?:\/\/[^\]]+\]$/, '').trim();
  }

  const isDouyinPlatform = anchorInfo.platform === 'douyin' || 
                         (typeof streamUrlObject === 'object' && 
                         (streamUrlObject.flv_pull_url && Object.keys(streamUrlObject.flv_pull_url).length > 0) &&
                         !backupUrl);
                         
  if (isDouyinPlatform && typeof streamUrlObject === 'object' && !backupUrl) {
    console.log(`[${getCurrentTime()}] 检测到抖音平台，尝试提取其他清晰度作为备用...`);
    if (mainUrl.includes('.m3u8') && streamUrlObject.hls_pull_url_map) {
      const currentQuality = Object.keys(streamUrlObject.hls_pull_url_map).find(
        key => streamUrlObject.hls_pull_url_map[key] === mainUrl
      );
      if (currentQuality) {
        const qualityIndex = ['FULL_HD1', 'HD1', 'SD1', 'SD2'].indexOf(currentQuality);
        if (qualityIndex > 0 && qualityIndex < 3) {
          const backupQuality = ['FULL_HD1', 'HD1', 'SD1', 'SD2'][qualityIndex + 1];
          if (streamUrlObject.hls_pull_url_map[backupQuality]) {
            backupUrl = streamUrlObject.hls_pull_url_map[backupQuality];
            console.log(`[${getCurrentTime()}] 选择${backupQuality}作为备用HLS链接`);
          }
        }
      }
    }
    if (!backupUrl && mainUrl.includes('.m3u8') && streamUrlObject.flv_pull_url) {
      const qualityMatch = mainUrl.match(/_([a-z]+)\.[m3]/i);
      if (qualityMatch && qualityMatch[1]) {
        const quality = qualityMatch[1];
        Object.keys(streamUrlObject.flv_pull_url).forEach(key => {
          const flvUrl = streamUrlObject.flv_pull_url[key];
          if (flvUrl.includes(`_${quality}.flv`)) {
            backupUrl = flvUrl;
            console.log(`[${getCurrentTime()}] 选择相同清晰度的FLV作为备用链接`);
            return;
          }
        });
      }
    }
  }

  const isHls = mainUrl.includes('.m3u8') || mainUrl.includes('/hls/');
  if (isHls) {
    console.log(`[${getCurrentTime()}] 检测到HLS流，正在验证: ${mainUrl}`);
    await appendAnchorLog(anchorId, `检测到HLS流，正在验证`);
    const isValid = await validateHlsStream(mainUrl, anchorId);
    
    if (!isValid && backupUrl) {
      console.log(`[${getCurrentTime()}] 主HLS流不可用，尝试备用链接: ${backupUrl}`);
      const isBackupValid = await validateHlsStream(backupUrl, anchorId);
      
      if (isBackupValid) {
        console.log(`[${getCurrentTime()}] 备用链接可用，切换到备用链接`);
        await appendAnchorLog(anchorId, `主HLS流不可用，切换到备用链接`);
        mainUrl = backupUrl;
        backupUrl = null; 
      } else if (typeof streamUrlObject === 'object') {
        console.log(`[${getCurrentTime()}] 主备链接均不可用，尝试降级清晰度...`);
        const qualities = ['SD2', 'SD1', 'HD1'];
        for (const q of qualities) {
          const lowerUrl = getUrlByQualityIndex(streamUrlObject, qualities.indexOf(q));
          if (lowerUrl && lowerUrl !== mainUrl && lowerUrl !== backupUrl) {
            const isLowerValid = await validateHlsStream(lowerUrl, anchorId);
              if (isLowerValid) {
                console.log(`[${getCurrentTime()}] 降级到${q}清晰度成功`);
                await appendAnchorLog(anchorId, `降级到${q}清晰度成功`);
                mainUrl = lowerUrl;
              currentQualityLevel = q;
                break;
              }
            }
          }
        }
      }
  }

  const outputDir = RECORDING_DIR;
  const subdir = path.join(outputDir, anchorId);
  await fs.promises.mkdir(subdir, { recursive: true });

  const taskStartTimestamp = Math.floor(Date.now() / 1000); // 整个录制任务的开始时间戳
  let currentSegmentStartTime = taskStartTimestamp; // 用于分段文件的时间戳
  let baseOutputFile = path.join(subdir, `${currentSegmentStartTime}-${anchorId}`);
  let outputFile = `${baseOutputFile}.ts`; // 期望的输出文件
  
  // 主输出文件
  let mainOutputFile = path.join(subdir, `${taskStartTimestamp}-${anchorId}.ts`);
  let currentOutputFile = mainOutputFile;
  // 移除segmentFiles数组，改为文件夹扫描方式
  let hasSourceChanged = false; // 是否发生过换源

  // 更新 activeRecordings，确保包含 latestOutputFile 和 subdir
  if (activeRecordings.has(anchorId)) {
    const currentRecording = activeRecordings.get(anchorId);
    currentRecording.latestOutputFile = currentOutputFile;
    currentRecording.subdir = subdir;
  } else {
    activeRecordings.set(anchorId, { 
        ffmpegProcess: null, 
        recordingPromise: null, 
        anchorInfo, 
        isStopping: false, 
        latestOutputFile: currentOutputFile,
        subdir: subdir,
        taskStartTimestamp: taskStartTimestamp
    });
  }

  await updateAnchorStatus(anchorId, { is_recording: true });
  await appendAnchorLog(anchorId, `更新录制状态为 true`);
  
  // 添加当前程序的 tag 到主播的 tag 字段
  await updateAnchorTag(anchorId, 'add');

  try {
    await fs.promises.access(subdir, fs.constants.W_OK);
  } catch (err) {
    console.error(`[${getCurrentTime()}] 无法写入目录 ${subdir}`);
    await appendAnchorLog(anchorId, `无法写入目录 ${subdir}`);
    throw new Error(`无权写入目录 ${subdir}`);
  }

  let retryCount = 0;
  const maxRetries = 5;
  let ffmpegProcess;
  let isStopping = false;
  let fileExistsAndNotEmpty = false;

  let currentUrl = mainUrl;
  const triedUrls = new Set();
  triedUrls.add(currentUrl);

  let freshUrlTried = false;
  let lowerQualityTried = false;
  
  let messageSentForCurrentSegment = false;

  const recordingPromise = new Promise(async (resolve, reject) => {
    try {
      // 移除了定期status检查，只在录制出错时检查
      
      if (activeRecordings.has(anchorId) && activeRecordings.get(anchorId).isStopping === true) {
        isStopping = true;
      }
      
      // 每次换源使用新文件，避免合并问题
      
      while (!isStopping) {
        messageSentForCurrentSegment = false;
        
        // 只有在成功换源时才创建新段文件
        if (hasSourceChanged && retryCount === 0) {
          // 使用当前时间戳创建新文件，而不是使用 -seg 后缀
          const newTimestamp = Math.floor(Date.now() / 1000);
          currentOutputFile = path.join(subdir, `${newTimestamp}-${anchorId}.ts`);
          await appendAnchorLog(anchorId, `准备开始录制新文件: ${path.basename(currentOutputFile)}`);
          
          // 更新 activeRecordings 中的最新文件路径
          if (activeRecordings.has(anchorId)) {
            activeRecordings.get(anchorId).latestOutputFile = currentOutputFile;
          }
          
          log(LOG_LEVEL.INFO, `换源成功，创建新文件: ${currentOutputFile}`, anchorId);
        }

        log(LOG_LEVEL.INFO, `开始录制: ${currentOutputFile}`, anchorId);
        log(LOG_LEVEL.INFO, `使用链接: ${currentUrl}`, anchorId);

        const isDouyinUrl = currentUrl.includes('douyincdn.com') || 
                           currentUrl.includes('douyinvod.com') || 
                           currentUrl.includes('douyinliving.com') ||
                           currentUrl.includes('flive.douyincdn.com');

        // 构建 ffmpeg 参数
        const ffmpegArgs = [
          '-loglevel', 'error',
          '-hide_banner',
          '-rw_timeout', '30000000', // 30秒超时
          '-reconnect', '1',
          '-reconnect_streamed', '1',
          '-reconnect_delay_max', '5',
          '-max_reload', '999999', // HLS重载次数
          '-m3u8_hold_counters', '999999', // HLS保持计数器
        ];

        // 为特定平台添加headers
        if (isDouyinUrl && (anchorInfo.platform === 'douyin' || anchorInfo.platform === 'tiktok')) {
          const douyinHeaders = buildDouyinHttpHeaders(anchorId);
          if (douyinHeaders) {
            // 将多行header转换为ffmpeg格式
            const headerLines = douyinHeaders.trim().split('\r\n');
            let headerString = headerLines.join('\r\n') + '\r\n';
            ffmpegArgs.push('-headers', headerString);
            log(LOG_LEVEL.DEBUG, `已为抖音链接应用自定义HTTP头部`, anchorId);
          } else {
            // 备用头部
            ffmpegArgs.push('-headers', 
              'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36\r\n' +
              'Referer: https://live.douyin.com/\r\n'
            );
            log(LOG_LEVEL.DEBUG, `应用备用HTTP头部`, anchorId);
          }
        } else if (anchorInfo.platform === 'youtube') {
          // YouTube特定headers
          ffmpegArgs.push('-headers',
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\r\n'
          );
        }
        
        // 输入URL
        ffmpegArgs.push('-i', currentUrl);
        
        // 输出参数
        ffmpegArgs.push(
          '-c:v', 'copy', // 视频流直接复制
          '-c:a', 'copy', // 音频流直接复制
          '-f', 'mpegts', // 使用mpegts格式，方便后续合并
          '-fflags', '+genpts+igndts', // 生成时间戳并忽略DTS错误
          '-use_wallclock_as_timestamps', '1', // 使用墙钟时间戳
          '-avoid_negative_ts', 'make_zero', // 避免负时间戳
          '-max_delay', '500000', // 设置最大延迟
          '-max_interleave_delta', '0' // 禁用交错以避免音频问题
        );
        
        // 输出到当前段文件
        
        // 在ffmpeg中也设置文件大小限制作为额外保险
        ffmpegArgs.push('-fs', (MAX_FILE_SIZE_BYTES * 0.98).toString()); // 设置为98%作为硬限制
        
        // 输出文件
        ffmpegArgs.push(currentOutputFile);

        let stderrOutput = '';
        let exitCode = null;
        const recordStartTime = Date.now();

        ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
        await appendAnchorLog(anchorId, `FFmpeg进程已启动，开始录制文件: ${path.basename(currentOutputFile)}`);

        if (activeRecordings.has(anchorId)) {
          activeRecordings.get(anchorId).ffmpegProcess = ffmpegProcess;
        }
        
        let errorBuffer = '';
        ffmpegProcess.stderr.on('data', async (data) => {
          const output = data.toString();
          stderrOutput += output;
          errorBuffer += output;
          
          // 只记录包含error的输出
          if (output.toLowerCase().includes('error') || output.toLowerCase().includes('fail')) {
            log(LOG_LEVEL.WARN, `ffmpeg stderr: ${output.trim()}`, anchorId);
            await appendAnchorLog(anchorId, `FFmpeg 错误: ${output.trim().substring(0, 200)}`);
          }
        });
        
        ffmpegProcess.errorBuffer = errorBuffer;
        

        exitCode = await new Promise((resolveExit) => {
          ffmpegProcess.on('exit', (code) => {
            log(LOG_LEVEL.INFO, `ffmpeg 进程退出，退出码: ${code}`, anchorId);
            resolveExit(code);
          });
          ffmpegProcess.on('error', async (err) => {
            log(LOG_LEVEL.ERROR, `ffmpeg 进程错误: ${err.message}`, anchorId);
            await appendAnchorLog(anchorId, `FFmpeg 进程错误: ${err.message}`);
            errorBuffer += `Spawn error: ${err.message}\n`;
            resolveExit(-1);
          });
        });
        
        if (activeRecordings.has(anchorId) && activeRecordings.get(anchorId).isStopping === true) {
            if (!isStopping) {
                log(LOG_LEVEL.INFO, `[recordStream] 外部停止信号检测到 (post ffmpeg exit)，同步内部isStopping。 anchor_id: ${anchorId}`);
                isStopping = true;
            }
        }

        if (isStopping) {
          log(LOG_LEVEL.INFO, `[recordStream] isStopping is true (ffmpeg exit code: ${exitCode}), 准备跳出录制循环. anchor_id: ${anchorId}`);
          break; 
        }

        // 检查文件是否已生成以及大小
        fileExistsAndNotEmpty = false;
        let finalFileSize = 0;
        try {
          const stat = await fs.promises.stat(currentOutputFile);
          if (stat.size > 0) {
            fileExistsAndNotEmpty = true;
            finalFileSize = stat.size;
          }
        } catch (e) {
          log(LOG_LEVEL.WARN, `检查输出文件 ${currentOutputFile} 失败: ${e.message}`, anchorId);
        }

        // 处理 ffmpeg 退出
        if (exitCode === 0 || (exitCode === 255 && fileExistsAndNotEmpty)) { // ffmpeg正常退出或被SIGINT中断但有数据
          if (fileExistsAndNotEmpty) {
            const recordDuration = Math.floor((Date.now() - recordStartTime) / 1000);
            log(LOG_LEVEL.INFO, `ffmpeg 录制完成: ${currentOutputFile}, 大小: ${finalFileSize} bytes`, anchorId);
            await appendAnchorLog(anchorId, `录制完成文件: ${path.basename(currentOutputFile)}, 大小: ${(finalFileSize / (1024 * 1024)).toFixed(2)} MB, 时长: ${recordDuration}秒`);
            
            // 文件已创建，将通过文件夹扫描方式处理
            
            // 如果是网络中断导致的退出，尝试继续录制
            if (exitCode === 0 && errorBuffer.includes('Server returned 4')) {
              log(LOG_LEVEL.INFO, `检测到网络中断，准备重连`, anchorId);
              await appendAnchorLog(anchorId, `检测到网络中断，准备重连`);
              retryCount = 0;
              freshUrlTried = false;
              continue;
            }
            
            // 检查是否因为文件大小限制退出
            // ffmpeg的-fs参数会让文件达到大小限制时正常退出(exit code 0)
            if (finalFileSize > (MAX_FILE_SIZE_BYTES * 0.95)) {
              log(LOG_LEVEL.INFO, `文件达到大小限制 (${(finalFileSize / (1024 * 1024)).toFixed(2)} MB)，创建新段继续录制`, anchorId);
              await appendAnchorLog(anchorId, `文件达到大小限制 (${(finalFileSize / (1024 * 1024)).toFixed(2)} MB)，创建新段继续录制`);
              
              // 检查主播是否仍在直播
              try {
                const { data: anchorStatus, error: statusError } = await supabase
                  .from('anchors')
                  .select('status, anchor_name')
                  .eq('anchor_id', anchorId)
                  .single();
                
                if (!statusError && anchorStatus && anchorStatus.status === true) {
                  log(LOG_LEVEL.INFO, `主播 ${anchorId} (${anchorStatus.anchor_name || anchorInfo.anchor_name || '未知'}) 仍在直播，继续录制新段`, anchorId);
                  await appendAnchorLog(anchorId, `主播仍在直播，创建新段继续录制`);
                  
                  // 创建新的段文件
                  currentOutputFile = path.join(subdir, `${Math.floor(Date.now() / 1000)}-${anchorId}.ts`);
                  await appendAnchorLog(anchorId, `开始录制新段文件: ${path.basename(currentOutputFile)}`);
                  
                  // 更新 activeRecordings 中的最新文件路径
                  if (activeRecordings.has(anchorId)) {
                    activeRecordings.get(anchorId).latestOutputFile = currentOutputFile;
                  }
                  
                  // 重置重试计数，继续录制
                  retryCount = 0;
                  freshUrlTried = false;
                  hasSourceChanged = false;
                  continue; // 继续录制循环
                } else if (!statusError && anchorStatus && anchorStatus.status === false) {
                  log(LOG_LEVEL.INFO, `主播 ${anchorId} (${anchorStatus.anchor_name || anchorInfo.anchor_name || '未知'}) 已下播，正常结束录制`, anchorId);
                  await appendAnchorLog(anchorId, `检测到主播已下播，正常结束录制`);
                  isStopping = true;
                } else {
                  // 查询失败，默认继续录制
                  log(LOG_LEVEL.WARN, `无法查询主播状态，默认继续录制`, anchorId);
                  currentOutputFile = path.join(subdir, `${Math.floor(Date.now() / 1000)}-${anchorId}.ts`);
                  await appendAnchorLog(anchorId, `连接重置，准备录制新文件: ${path.basename(currentOutputFile)}`);
                  retryCount = 0;
                  freshUrlTried = false;
                  hasSourceChanged = false;
                  continue;
                }
              } catch (err) {
                log(LOG_LEVEL.ERROR, `检查主播状态时出错: ${err.message}，默认继续录制`, anchorId);
                // 出错时默认继续录制
                currentOutputFile = path.join(subdir, `${Math.floor(Date.now() / 1000)}-${anchorId}.ts`);
                await appendAnchorLog(anchorId, `默认继续录制，新文件: ${path.basename(currentOutputFile)}`);
                retryCount = 0;
                freshUrlTried = false;
                hasSourceChanged = false;
                continue;
              }
            } else {
              // 其他原因的正常结束
              log(LOG_LEVEL.INFO, `ffmpeg 正常结束，结束录制 (anchor_id: ${anchorId})`, anchorId);
              isStopping = true;
            }
          } else {
            log(LOG_LEVEL.WARN, `ffmpeg 退出但输出文件 ${currentOutputFile} 为空或不存在。`, anchorId);
          }
        }
        
        // 错误处理逻辑
        if (exitCode !== 0 && exitCode !== 255) {
          log(LOG_LEVEL.ERROR, `[recordStream] ffmpeg 录制失败 (exitCode: ${exitCode})，链接: ${currentUrl.substring(0,100)}...`, anchorId);
          await appendAnchorLog(anchorId, `FFmpeg 录制失败 (exitCode: ${exitCode}), 文件: ${path.basename(currentOutputFile)}, 大小: ${finalFileSize} bytes`);
          
          // 所有非正常退出都检查status状态
          log(LOG_LEVEL.INFO, "[recordStream] 录制异常退出，检查主播status状态", anchorId);
          
          try {
            const { data: anchorStatus, error: statusError } = await supabase
              .from('anchors')
              .select('status, anchor_name')
              .eq('anchor_id', anchorId)
              .single();
            
            if (statusError) {
              log(LOG_LEVEL.ERROR, `[recordStream] 查询主播status失败: ${statusError.message}，继续正常重试流程`, anchorId);
            } else if (anchorStatus && anchorStatus.status === false) {
              log(LOG_LEVEL.INFO, `[recordStream] 检测到主播 ${anchorId} (${anchorStatus.anchor_name || anchorInfo.anchor_name || '未知'}) status=false，判定直播已结束，停止录制`, anchorId);
              await appendAnchorLog(anchorId, `检测到 status=false，直播已结束，停止录制`);
              isStopping = true;
              break;
            } else {
              log(LOG_LEVEL.INFO, `[recordStream] 主播 ${anchorId} (${anchorStatus?.anchor_name || anchorInfo.anchor_name || '未知'}) status=${anchorStatus?.status}，继续重试流程`, anchorId);
              
              // 特殊处理：404错误时重置freshUrlTried
              if (errorBuffer.includes('404 Not Found') || errorBuffer.includes('HTTP Error 404')) {
                log(LOG_LEVEL.WARN, "[recordStream] 检测到404错误", anchorId);
                await appendAnchorLog(anchorId, `检测到404错误，链接可能已失效`);
                freshUrlTried = false;
              }
            }
          } catch (statErr) {
            log(LOG_LEVEL.ERROR, `[recordStream] 检查主播status时发生异常: ${statErr.message}`, anchorId);
          }
          
          if (isStopping) {
            log(LOG_LEVEL.INFO, `[recordStream] 因检测到直播已结束，终止重试流程`, anchorId);
            break;
          }

          await incrementCount(anchorId, 'bcount');

          // 创建一个内部函数来检查status
          const checkStatusBeforeRetry = async (actionName) => {
            try {
              const { data: anchorStatus, error: statusError } = await supabase
                .from('anchors')
                .select('status, anchor_name')
                .eq('anchor_id', anchorId)
                .single();
              
              if (statusError) {
                log(LOG_LEVEL.ERROR, `[recordStream] ${actionName}前查询主播status失败: ${statusError.message}`, anchorId);
                return true; // 查询失败时继续重试
              }
              
              if (anchorStatus && anchorStatus.status === false) {
                log(LOG_LEVEL.INFO, `[recordStream] ${actionName}前检测到主播 ${anchorId} (${anchorStatus.anchor_name || anchorInfo.anchor_name || '未知'}) status=false，停止重试`, anchorId);
                await appendAnchorLog(anchorId, `${actionName}前检测到 status=false，停止重试`);
                isStopping = true;
                return false; // 返回false表示不应继续
              }
              
              return true; // 返回true表示可以继续
            } catch (err) {
              log(LOG_LEVEL.ERROR, `[recordStream] ${actionName}前检查status时发生异常: ${err.message}`, anchorId);
              return true; // 异常时继续重试
            }
          };

          // 重试逻辑（与原代码相同）
          if (retryCount < 2) {
            if (!await checkStatusBeforeRetry(`第${retryCount + 1}次重试`)) break;
            
            retryCount++;
            log(LOG_LEVEL.INFO, `[recordStream] 当前链接错误，ffmpeg直接重试第${retryCount}次`, anchorId);
            await new Promise(r => setTimeout(r, 5000 + retryCount * 3000)); 
            continue;
          }
          
          // 尝试获取新链接等逻辑（保持原有逻辑）
          if (!freshUrlTried) {
            if (!await checkStatusBeforeRetry('获取数据库最新链接')) break;
            
            log(LOG_LEVEL.INFO, `[recordStream] 尝试获取数据库最新的链接 (ffmpeg)`, anchorId);
            await appendAnchorLog(anchorId, `尝试获取数据库最新的直播链接`);
            freshUrlTried = true; 
            
            const newStreamUrlObj = await getLatestStreamUrl(anchorId);
            if (newStreamUrlObj) {
              const newAnchorName = (typeof newStreamUrlObj === 'object' && newStreamUrlObj._anchor_name) ? newStreamUrlObj._anchor_name : anchorInfo.anchor_name;
              const newAnchorNewUrl = (typeof newStreamUrlObj === 'object' && newStreamUrlObj._new_url) ? newStreamUrlObj._new_url : anchorInfo._new_url;
              if (newAnchorName) anchorInfo.anchor_name = newAnchorName;
              if (newAnchorNewUrl) anchorInfo._new_url = newAnchorNewUrl;

              const newHlsUrl = extractBestStreamUrl(newStreamUrlObj, null, anchorInfo.platform);
              
              if (newHlsUrl && !triedUrls.has(newHlsUrl)) {
                log(LOG_LEVEL.INFO, `[recordStream] 成功获取数据库最新链接 (ffmpeg)，将切换`, anchorId);
                log(LOG_LEVEL.DEBUG, `[recordStream] 旧链接: ${currentUrl.substring(0,100)}...`, anchorId);
                log(LOG_LEVEL.INFO, `[recordStream] 新链接: ${newHlsUrl.substring(0,100)}...`, anchorId);
                
                currentUrl = newHlsUrl;
                currentQualityLevel = extractQualityFromUrl(newHlsUrl);
                triedUrls.add(newHlsUrl);
                retryCount = 0;
                hasSourceChanged = true; // 标记换源
                continue;
              }
            }
          }

          // 其余重试逻辑保持不变...
          if (backupUrl && currentUrl !== backupUrl && !triedUrls.has(backupUrl)) {
            if (!await checkStatusBeforeRetry('切换到备用链接')) break;
            
            log(LOG_LEVEL.INFO, `[recordStream] 尝试切换到备用链接 (ffmpeg): ${backupUrl.substring(0,100)}...`, anchorId);
            currentUrl = backupUrl;
            triedUrls.add(backupUrl);
            retryCount = 0;
            freshUrlTried = false;
            hasSourceChanged = true; // 标记换源
            continue;
          }

          // 备用链接列表
          if (streamUrlObject._backup_urls && streamUrlObject._backup_urls.length > 0) {
            if (!await checkStatusBeforeRetry('尝试备用链接列表')) break;
            
            let switchedToBackupListUrl = false;
            for (let i = 0; i < streamUrlObject._backup_urls.length; i++) {
                const nextBackup = streamUrlObject._backup_urls[i];
                if (nextBackup.url && !triedUrls.has(nextBackup.url)) {
                    log(LOG_LEVEL.INFO, `[recordStream] 尝试切换到备用列表链接 ${i}: ${nextBackup.url.substring(0,100)}... (画质: ${nextBackup.quality}, 类型: ${nextBackup.type})`, anchorId);
                    currentUrl = nextBackup.url;
                    currentQualityLevel = nextBackup.quality;
                    triedUrls.add(currentUrl);
                    retryCount = 0;
                    freshUrlTried = false;
                    hasSourceChanged = true; // 标记换源
                    switchedToBackupListUrl = true;
                    break; 
                }
            }
            if (switchedToBackupListUrl) continue;
          }

          // 降级画质
          if (isHls && !lowerQualityTried && typeof streamUrlObject === 'object') {
            if (!await checkStatusBeforeRetry('降级画质')) break;
            
            log(LOG_LEVEL.INFO, `[recordStream] 尝试获取次一级画质链接 (ffmpeg)`, anchorId);
            lowerQualityTried = true;
            
            const qualityHierarchy = ['FULL_HD1', 'HD1', 'SD1', 'SD2'];
            let currentQualityIdx = qualityHierarchy.indexOf(currentQualityLevel);
            if (currentQualityIdx === -1 && mainUrl === extractBestStreamUrl(streamUrlObject, null, anchorInfo.platform)) {
                 currentQualityIdx = 0;
            }

            if (currentQualityIdx < qualityHierarchy.length - 1 && currentQualityIdx !== -1) {
              const nextLowerQuality = qualityHierarchy[currentQualityIdx + 1];
              const nextLowerUrl = extractSpecificQualityUrl(streamUrlObject, nextLowerQuality, true);

              if (nextLowerUrl && !triedUrls.has(nextLowerUrl)) {
                log(LOG_LEVEL.INFO, `[recordStream] 成功获取到次一级画质 (${nextLowerQuality}) 链接 (ffmpeg): ${nextLowerUrl.substring(0,100)}...`, anchorId);
                currentUrl = nextLowerUrl;
                currentQualityLevel = nextLowerQuality;
                triedUrls.add(currentUrl);
                retryCount = 0;
                freshUrlTried = false;
                hasSourceChanged = true; // 标记换源
                continue;
              }
            }
          }

          if (retryCount >= maxRetries -1 ) {
            log(LOG_LEVEL.ERROR, `[recordStream] 所有重试和链接切换尝试均失败，放弃录制 (ffmpeg). anchor_id: ${anchorId}`, anchorId);
            
            try {
              const { data: anchorStatus, error: statusError } = await supabase
                .from('anchors')
                .select('status, anchor_name')
                .eq('anchor_id', anchorId)
                .single();
              
              if (!statusError && anchorStatus && anchorStatus.status === false) {
                log(LOG_LEVEL.INFO, `[recordStream] 放弃录制前检测到主播 ${anchorId} (${anchorStatus.anchor_name || anchorInfo.anchor_name || '未知'}) status=false，判定为正常结束录制`, anchorId);
                isStopping = true;
                break;
              }
            } catch (statErr) {
              log(LOG_LEVEL.ERROR, `[recordStream] 放弃录制前检查主播status时发生异常: ${statErr.message}`, anchorId);
            }
            
            isStopping = true;
            reject(new Error(`ffmpeg 录制失败，已达最大重试次数`));
          } else {
            retryCount++; 
            log(LOG_LEVEL.INFO, `[recordStream] 第 ${retryCount}/${maxRetries-1} 次尝试失败后，准备进行下一次尝试 (ffmpeg)`, anchorId);
            await new Promise(r => setTimeout(r, 15000 + retryCount * 5000));
          }
        }
        
        if (exitCode === 0 && fileExistsAndNotEmpty) {
            if (freshUrlTried || lowerQualityTried) {
                log(LOG_LEVEL.DEBUG, "[recordStream] ffmpeg 成功完成一段录制，重置 freshUrlTried 和 lowerQualityTried 标志", anchorId);
                freshUrlTried = false;
                lowerQualityTried = false;
            }
        }
      } // End of while (!isStopping)

      // 处理最后的文件
      if (isStopping) {
        log(LOG_LEVEL.INFO, `录制循环已停止 (anchor_id: ${anchorId})。等待主播下播后处理文件`, anchorId);
        
        // 保存录制目录信息，等待主播下播后扫描文件夹进行处理
        log(LOG_LEVEL.INFO, `录制结束，文件保存在目录: ${subdir}，等待主播下播后拼接处理`, anchorId);
        
        // 更新 activeRecordings，保存目录信息
        if (activeRecordings.has(anchorId)) {
          const recordingInfo = activeRecordings.get(anchorId);
          recordingInfo.taskStartTimestamp = taskStartTimestamp;
          recordingInfo.subdir = subdir;
        }
        
        // 清理空文件
        try {
          const files = await fs.promises.readdir(subdir);
          for (const file of files) {
            const filePath = path.join(subdir, file);
            try {
              const stat = await fs.promises.stat(filePath);
              if (stat.size === 0) {
                await fs.promises.unlink(filePath);
                log(LOG_LEVEL.DEBUG, `删除空文件: ${file}`, anchorId);
              }
            } catch (e) {
              log(LOG_LEVEL.WARN, `检查文件时出错: ${e.message}`, anchorId);
            }
          }
        } catch (e) {
          log(LOG_LEVEL.WARN, `清理空文件时出错: ${e.message}`, anchorId);
        }
      }


      
      await updateAnchorStatus(anchorId, { is_recording: false });
      await updateAnchorTag(anchorId, 'remove');
      log(LOG_LEVEL.INFO, `录制任务结束 (anchor_id: ${anchorId})`, anchorId);
      // 文件处理将在 handleAnchorStreamEnd 中异步进行
      
      resolve();
    } catch (err) {
      logError(`[recordStream] 录制主逻辑发生未捕获异常`, err, anchorId);
      activeRecordings.delete(anchorId);
      await updateAnchorStatus(anchorId, { is_recording: false });
      await updateAnchorTag(anchorId, 'remove');
      reject(err);
    }
  });

  return { ffmpegProcess, recordingPromise, isStopping };
}

/**
 * 检查并停止需要结束的录制（status=false 但 is_recording=true）
 */
async function checkAndStopRecordingsWithStatusFalse() {
  log(LOG_LEVEL.INFO, `开始检查需要停止的录制（status=false 但 is_recording=true）...`);
  
  try {
    // 查询当前区间内 is_recording=true 但 status=false 的情况，这表示主播已下播需要停止录制
    const { data: recordingsToStop, error } = await supabase
      .from('anchors')
      .select('anchor_id, anchor_name, platform, is_recording, status')
      .eq('is_recording', true)
      .eq('status', false)
      .order('timestamp', { ascending: true })
      .range(currentOffset, currentOffset + currentLimit - 1);
    
    if (error) {
      log(LOG_LEVEL.ERROR, `查询需要停止的录制时出错: ${error.message}`);
      return;
    }
    
    if (!recordingsToStop || recordingsToStop.length === 0) {
      log(LOG_LEVEL.INFO, `未发现需要停止的录制`);
      return;
    }
    
    log(LOG_LEVEL.INFO, `发现 ${recordingsToStop.length} 个需要停止的录制（status=false 但 is_recording=true）`);
    
    for (const recording of recordingsToStop) {
      const { anchor_id, anchor_name, platform } = recording;
      
      log(LOG_LEVEL.INFO, `检测到主播已下播，需要停止录制: ${anchor_id} (${anchor_name}) - status=false`);
      
      // 检查是否在 activeRecordings 中
      if (activeRecordings.has(anchor_id)) {
        log(LOG_LEVEL.INFO, `正在停止录制: ${anchor_id} (${anchor_name}）`);
        
        const recordingInfo = activeRecordings.get(anchor_id);
        recordingInfo.isStopping = true;
        
        if (recordingInfo.ffmpegProcess && !recordingInfo.ffmpegProcess.killed) {
          recordingInfo.ffmpegProcess.kill('SIGINT');
        }
        
        // 等待录制任务结束
        if (recordingInfo.recordingPromise) {
          try {
            await Promise.race([
              recordingInfo.recordingPromise,
              new Promise(resolve => setTimeout(resolve, 10000)) // 10秒超时
            ]);
          } catch (err) {
            log(LOG_LEVEL.WARN, `等待录制结束时出错: ${err.message}`, anchor_id);
          }
        }
        
        // 确保从 activeRecordings 中移除
        activeRecordings.delete(anchor_id);
      } else {
        log(LOG_LEVEL.INFO, `录制 ${anchor_id} (${anchor_name}) 不在 activeRecordings 中，仅更新数据库状态`);
      }
      
      // 更新数据库状态
      await updateAnchorStatus(anchor_id, { 
        is_recording: false
      });
      await updateAnchorTag(anchor_id, 'remove');
      
      log(LOG_LEVEL.INFO, `已停止录制: ${anchor_id} (${anchor_name})`);
      
      // 调用 handleAnchorStreamEnd 处理录制的文件
      log(LOG_LEVEL.INFO, `调用 handleAnchorStreamEnd 处理主播 ${anchor_id} 的录制文件`);
      await handleAnchorStreamEnd(anchor_id, anchor_name, platform, false);
    }
    
    log(LOG_LEVEL.INFO, `录制停止检查完成，共处理 ${recordingsToStop.length} 个录制`);
    
  } catch (err) {
    log(LOG_LEVEL.ERROR, `检查需要停止的录制时发生错误: ${err.message}`);
  }
}

/**
 * 检查并重新启动遗漏录制
 */
async function checkAndRestartMissedRecordings() {
  log(LOG_LEVEL.INFO, `开始检查并重新启动遗漏的录制条目...`);
  
  // 移除磁盘空间检查，让清理脚本自动处理空间问题
  // if (isDiskSpaceLow) {
  //   log(LOG_LEVEL.WARN, `磁盘空间不足，跳过检查遗漏的录制条目`);
  //   return;
  // }
  
  try {
    // 不再限制为 record = true 的条目
    const { data: anchorsInRange, error } = await supabase
      .from('anchors')
      .select('anchor_id, stream_url, finishtime, ordertime, status, is_recording, record, timestamp, anchor_name, platform')
      .order('timestamp', { ascending: true })
      .range(currentOffset, currentOffset + currentLimit - 1);

    if (error) {
      log(LOG_LEVEL.ERROR, `获取当前区间内的anchors时出错: ${error.message}`);
      return;
    }

    // 添加更详细的统计信息
    log(LOG_LEVEL.INFO, `在区间 [${currentOffset},${currentOffset + currentLimit - 1}] 内共找到 ${anchorsInRange ? anchorsInRange.length : 0} 条记录`);
    
    if (!anchorsInRange || anchorsInRange.length === 0) {
      log(LOG_LEVEL.INFO, `没有找到记录，请检查数据库配置`);
      return;
    }
    
    // 增加详细统计
    let statusTrueCount = 0;
    let notRecordingCount = 0;
    let needRecordCount = 0;
    let hasStreamUrlCount = 0;
    let noStreamUrlCount = 0;
    let isRecordingCount = 0;
    
    // 检查哪些主播已经在录制中
    const recordingAnchors = [];
    const statusTrueNotRecordingAnchors = [];
    
    const currentTime = new Date();
    let resendCount = 0;
    
    for (const recording of anchorsInRange) {
      // 把已经在录制中的记录一下
      if (recording.is_recording === true) {
        isRecordingCount++;
        recordingAnchors.push({
          anchor_id: recording.anchor_id,
          anchor_name: recording.anchor_name,
          platform: recording.platform,
          timestamp: recording.timestamp
        });
      }
      
      // 统计 status=true 的记录
      if (recording.status === true) {
        statusTrueCount++;
        
        // 检查 record 字段
        if (recording.record === false) {
          log(LOG_LEVEL.DEBUG, `跳过 record=false 的主播: ${recording.anchor_id} (${recording.anchor_name})`);
          continue;
        }
        
        // 统计未在录制中的记录
        if (recording.is_recording === false) {
          notRecordingCount++;
          
          statusTrueNotRecordingAnchors.push({
            anchor_id: recording.anchor_id,
            anchor_name: recording.anchor_name,
            platform: recording.platform,
            finishtime: recording.finishtime,
            ordertime: recording.ordertime,
            stream_url: !!recording.stream_url
          });
          
          let shouldResend = false;
          let reason = '';

          // 检查是否有stream_url，没有的话记录一下
          if (!recording.stream_url) {
            noStreamUrlCount++;
            log(LOG_LEVEL.WARN, `主播 ${recording.anchor_id} (${recording.anchor_name}) 缺少stream_url，跳过录制`, recording.anchor_id);
            continue;
          } else {
            hasStreamUrlCount++;
          }

          // 检查录制条件：status=true, record=true, is_recording=false
          shouldResend = true;
          reason = `status=true且record=true且is_recording=false，触发检查立即录制`;
          needRecordCount++;

          if (shouldResend) {
            // 检查是否已经在activeRecordings中
            if (activeRecordings.has(recording.anchor_id)) {
              log(LOG_LEVEL.INFO, `主播 ${recording.anchor_id} (${recording.anchor_name}) 已在activeRecordings中，跳过重复录制`);
            } else {
              log(LOG_LEVEL.INFO, 
                `重新开始录制主播 ${recording.anchor_id}, anchor_name=${recording.anchor_name}, platform=${recording.platform}，原因: ${reason}`);
              // 添加 await 确保 startRecording 完成后再继续
              await startRecording(recording.anchor_id, recording.stream_url);
              await incrementCount(recording.anchor_id, 'bcount');
              resendCount++;
            }
          }
        } else {
          log(LOG_LEVEL.DEBUG, `主播 ${recording.anchor_id} (${recording.anchor_name}) 已经在录制中，跳过`, recording.anchor_id);
        }
      }
    }
    
    
    // 不再打印录制中的主播列表，避免日志过多
    
    // 输出本机实际正在录制的主播列表
    if (activeRecordings.size > 0) {
      log(LOG_LEVEL.INFO, `本机实际正在录制的主播列表(${activeRecordings.size}个)：`);
      let idx = 1;
      for (const [anchorId, { anchorInfo }] of activeRecordings.entries()) {
        log(LOG_LEVEL.INFO, `${idx++}. anchor_id: ${anchorId}, anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}`);
      }
    } else {
      log(LOG_LEVEL.INFO, `本机当前没有正在录制的主播`);
    }
    
    // 不再打印status=true但未录制的主播列表，可以直接从数据库查询

    // 输出统计信息摘要
    log(LOG_LEVEL.INFO, `检查完毕，状态摘要: 
    - status=true: ${statusTrueCount}/${anchorsInRange.length}条
    - 正在录制 (is_recording=true): ${isRecordingCount}/${anchorsInRange.length}条
    - 未录制 (is_recording=false): ${notRecordingCount}/${statusTrueCount}条
    - 有流地址: ${hasStreamUrlCount}/${notRecordingCount}条
    - 无流地址: ${noStreamUrlCount}/${notRecordingCount}条
    - 需要录制: ${needRecordCount}/${notRecordingCount}条
    - 成功开始: ${resendCount}/${needRecordCount}条
`);
    
  } catch (err) {
    logError(`检查并重新启动遗漏的录制时发生异常`, err);
  }
}

/**
 * 优雅关闭逻辑
 */
const gracefulShutdown = async () => {
  console.log(`\n[${getCurrentTime()}] 接收到退出信号，正在关闭录制任务...`);
  shuttingDown = true; // 设置全局关闭标志

  const stopPromises = [];
  const anchorIdsToUpdate = Array.from(activeRecordings.keys());

  // 第一阶段：设置所有录制任务的isStopping标志，并立即尝试SIGINT
  for (const [anchorId, recordingInfo] of activeRecordings.entries()) { // Changed variable name from recording to recordingInfo
    const anchorInfo = recordingInfo.anchorInfo || { anchor_name: '未知', platform: '未知' };
    console.log(
      `[${getCurrentTime()}] 优雅终止主播 ${anchorId} (anchor_name: ${anchorInfo.anchor_name}, platform: ${anchorInfo.platform}) 的录制任务...`
    );
    
    // 直接修改activeRecordings中的isStopping标志
    recordingInfo.isStopping = true;
    
    // 立即尝试发送 SIGINT
    const { ffmpegProcess } = recordingInfo;
    if (ffmpegProcess && !ffmpegProcess.killed) {
      log(LOG_LEVEL.INFO, `[Graceful Shutdown] 发送 SIGINT 到 ffmpeg 进程 for anchor_id: ${anchorId}`);
      ffmpegProcess.kill('SIGINT');
    }
    
    if (recordingInfo.recordingPromise) {
      // 创建一个Promise，它会等待录制任务正常完成，或在超时后强制终止
      stopPromises.push(
        Promise.race([
          // 等待录制任务正常完成
          recordingInfo.recordingPromise.catch(err => { // Changed variable name
            log(LOG_LEVEL.WARN, `录制任务结束时发生错误: ${err.message}`, anchorId);
          }),
          
          // 设置超时，如果SIGINT后等待太久，强制终止进程
          new Promise(resolve => {
            setTimeout(async () => { // Make this async to use await inside
              log(LOG_LEVEL.WARN, `等待录制任务(SIGINT后)正常结束超时，将强制终止 SIGKILL`, anchorId);
              // 再次检查 activeRecordings 是否还有该条目，以及进程是否仍在运行
              if (activeRecordings.has(anchorId)) {
                const currentRec = activeRecordings.get(anchorId);
                if (currentRec && currentRec.ffmpegProcess && !currentRec.ffmpegProcess.killed) {
                  log(LOG_LEVEL.WARN, `ffmpeg进程未响应SIGINT，发送SIGKILL强制终止`, anchorId);
                  currentRec.ffmpegProcess.kill('SIGKILL');
                }

                // 检查是否需要处理文件
                if (currentRec.latestOutputFile) {
                  // 检查是否有处理锁，如果有说明正在处理中
                  const lockKey = `${anchorId}:${currentRec.subdir || path.join('recordings', anchorId)}`;
                  const hasLock = processingLocks.has(lockKey);
                  
                  if (!hasLock) {
                    log(LOG_LEVEL.INFO, `[Graceful Shutdown Timeout] 无处理锁，尝试补发最后一个文件段 for ${anchorId}: ${currentRec.latestOutputFile}`);
                    await trySendLastSegment(anchorId, currentRec.latestOutputFile, currentRec);
                  } else {
                    log(LOG_LEVEL.INFO, `[Graceful Shutdown Timeout] 检测到处理锁，文件正在处理中 for ${anchorId}`);
                  }
                } else {
                  log(LOG_LEVEL.WARN, `[Graceful Shutdown Timeout] 无法补发 for ${anchorId}: latestOutputFile 为空`);
                }
              }
              resolve();
            }, 10000); // 例如，给10秒让SIGINT生效并清理，否则SIGKILL
          })
        ])
      );
    }
  }

  log(LOG_LEVEL.INFO, "等待所有录制任务完成或超时...");
  await Promise.allSettled(stopPromises); // 等待所有录制任务结束或超时
  
  log(LOG_LEVEL.INFO, "所有录制进程已停止，更新数据库状态并确认文件已发送...");

  // 最后一次检查，确保所有活跃录制的最后文件段都已发送
  for (const anchorId of anchorIdsToUpdate) {
    if (activeRecordings.has(anchorId)) {
      const currentRec = activeRecordings.get(anchorId);
      
      // 再次尝试补发文件，以防前面的尝试失败
      if (currentRec.latestOutputFile) {
        const lockKey = `${anchorId}:${currentRec.subdir || path.join('recordings', anchorId)}`;
        const hasLock = processingLocks.has(lockKey);
        
        if (!hasLock) {
          log(LOG_LEVEL.INFO, `[Graceful Shutdown Final Check] 最终检查：尝试补发文件段 for ${anchorId}: ${currentRec.latestOutputFile}`);
          await trySendLastSegment(anchorId, currentRec.latestOutputFile, currentRec);
        }
      }
      
      log(LOG_LEVEL.INFO, `[Graceful Shutdown] 更新主播 ${anchorId} 状态为未录制`);
      await updateAnchorStatus(anchorId, { is_recording: false });
      await updateAnchorTag(anchorId, 'remove');
      activeRecordings.delete(anchorId); // 从activeRecordings中移除
    }
  }
  
  activeRecordings.clear(); // 确保清空

  // 关闭Supabase连接
  if (supabase && typeof supabase.removeAllChannels === 'function') { // 检查supabase对象和方法是否存在
    try {
      await supabase.removeAllChannels();
      log(LOG_LEVEL.INFO, "Supabase channels 已移除。");
    } catch (e) {
      log(LOG_LEVEL.WARN, `移除 Supabase channels 时出错: ${e.message}`);
    }
  }

  console.log(`[${getCurrentTime()}] 程序已安全退出`);
  process.exit(0);
};

process.on('SIGINT', gracefulShutdown);

/**
 * 监听 does 表的变化
 * 注释掉这个监听器，因为下面已经有一个更完整的监听器处理 scanall 变化
 */
// supabase
//   .channel('does-changes')
//   .on(
//     'postgres_changes',
//     {
//       event: 'UPDATE',
//       schema: 'public',
//       table: 'does',
//     },
//     onDoesChange
//   )
//   .subscribe();

/**
 * 当 anchors 表发生变化时的回调函数
 */
const onAnchorChange = async (payload) => {
  // 检查payload是否有错误，如果有错误则跳过处理并记录错误
  if (payload.errors && payload.errors.length > 0) {
    log(LOG_LEVEL.ERROR, `[onAnchorChange] Payload errors: ${JSON.stringify(payload.errors)}`);
    return; // 跳过处理有错误的payload
  }
  
  // 检查是否有有效的new或old记录
  if (!payload.new && !payload.old) {
    log(LOG_LEVEL.WARN, `[onAnchorChange] Payload中没有有效的new或old记录，跳过处理`);
    return;
  }
  
  log(LOG_LEVEL.DEBUG, `[onAnchorChange] anchors 表发生变化 - eventType: ${payload.eventType}`);
  
  // 移除详细的payload日志打印，只保留关键信息
  // log(LOG_LEVEL.DEBUG, `[onAnchorChange] anchors 表发生变化: ${JSON.stringify(payload)}`);
  const newRecord = payload.new;
  const oldRecord = payload.old;

  // 处理主播下播 (status: true -> false)
  if (payload.eventType === 'UPDATE' && 
      newRecord && 
      oldRecord && 
      oldRecord.status === true && 
      newRecord.status === false) {
    
    const anchorId = newRecord.anchor_id;
    const anchorName = newRecord.anchor_name || '未知';
    const platform = newRecord.platform || '未知';
    const isRecording = newRecord.is_recording;
    
    log(LOG_LEVEL.INFO, `[onAnchorChange] 检测到主播 ${anchorId} (${anchorName}) 下播 (status: true->false, is_recording: ${isRecording})`);
    
    // 记录到主播日志
    await appendAnchorLog(anchorId, `检测到主播下播，status: true->false, is_recording: ${isRecording}`);
    
    // 强制结束录制处理逻辑优化
    await handleAnchorStreamEnd(anchorId, anchorName, platform, isRecording);
  }
  
  // 处理主播开播 (status: false -> true)
  if (payload.eventType === 'UPDATE' && 
      newRecord && 
      oldRecord && 
      oldRecord.status === false && 
      newRecord.status === true && 
      newRecord.is_recording === false) {
    
    const anchorId = newRecord.anchor_id;
    const anchorName = newRecord.anchor_name || '未知';
    const platform = newRecord.platform || '未知';

    log(LOG_LEVEL.INFO, `[onAnchorChange] 检测到主播 ${anchorId} (${anchorName}) 开播 (status: false->true, is_recording: false)`);
    
    // 1. 先检查主播是否在当前范围内，再决定是否记录日志
    // 使用数据库中的 timestamp 字段（即主播在表中的位置）来判断
    if (currentLimit > 0 && currentOffset >= 0) {
      try {
        // 获取主播在anchors表中的位置
        const { data: anchorData, error: positionError } = await supabase
          .from('anchors')
          .select('anchor_id')
          .order('timestamp', { ascending: true })
          .range(0, currentOffset + currentLimit - 1);
        
        if (positionError) {
          log(LOG_LEVEL.ERROR, `[onAnchorChange] 获取主播位置时出错: ${positionError.message}`);
          return;
        }
        
        // 查找当前主播在列表中的位置
        const anchorPosition = anchorData.findIndex(a => a.anchor_id === anchorId);
        
        if (anchorPosition === -1 || anchorPosition < currentOffset || anchorPosition >= currentOffset + currentLimit) {
          log(LOG_LEVEL.INFO, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) 位置 ${anchorPosition} 不在当前范围 [${currentOffset}, ${currentOffset + currentLimit}) 内，跳过录制`);
          // 不记录到主播日志，因为不在本程序的录制范围内
          return;
        }
        
        log(LOG_LEVEL.INFO, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) 位置 ${anchorPosition} 在当前范围 [${currentOffset}, ${currentOffset + currentLimit}) 内，继续处理`);
        
        // 只有在范围内才记录到主播日志
        await appendAnchorLog(anchorId, `检测到主播开播，status: false->true, is_recording: false`);
        
        // 检查 record 字段
        if (newRecord.record === false) {
          log(LOG_LEVEL.DEBUG, `[onAnchorChange] 跳过 record=false 的主播: ${anchorId} (${anchorName})`);
          await appendAnchorLog(anchorId, `跳过录制，record字段为false`);
          return;
        }
      } catch (err) {
        log(LOG_LEVEL.ERROR, `[onAnchorChange] 检查主播范围时出错: ${err.message}`);
        // 出错时不阻止录制，继续执行
        await appendAnchorLog(anchorId, `检测到主播开播，status: false->true, is_recording: false`);
        
        // 检查 record 字段
        if (newRecord.record === false) {
          log(LOG_LEVEL.DEBUG, `[onAnchorChange] 跳过 record=false 的主播: ${anchorId} (${anchorName})`);
          await appendAnchorLog(anchorId, `跳过录制，record字段为false`);
          return;
        }
      }
    } else {
      // 如果没有设置范围限制，正常记录日志
      await appendAnchorLog(anchorId, `检测到主播开播，status: false->true, is_recording: false`);
      
      // 检查 record 字段
      if (newRecord.record === false) {
        log(LOG_LEVEL.DEBUG, `[onAnchorChange] 跳过 record=false 的主播: ${anchorId} (${anchorName})`);
        await appendAnchorLog(anchorId, `跳过录制，record字段为false`);
        return;
      }
    }


    // 2. 移除磁盘空间检查，让清理脚本自动处理空间问题
    // if (isDiskSpaceLow) {
    //   log(LOG_LEVEL.WARN, `[onAnchorChange] 磁盘空间不足，跳过主播 ${anchorId} (${anchorName}) 的录制`);
    //   return;
    // }

    // 3. 检查是否已在录制 (理论上 is_recording 应该是 false，但双重检查)
    if (activeRecordings.has(anchorId)) {
      log(LOG_LEVEL.INFO, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) 已在 activeRecordings 中，可能已被其他逻辑启动，跳过。`);
      await appendAnchorLog(anchorId, `主播已在 activeRecordings 中，跳过重复录制`);
      return;
    }

    // 4. 获取 stream_url 并开始录制
    const streamUrlData = newRecord.stream_url;
    if (!streamUrlData) {
      log(LOG_LEVEL.WARN, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) 开播但 stream_url 为空，无法开始录制。`);
      await appendAnchorLog(anchorId, `开播但 stream_url 为空，无法开始录制`);
      // 可以在这里尝试调用一次 getLatestStreamUrl，或者依赖后续的 checkAndRestartMissedRecordings
      return;
    }

    // 双重检查，避免竞态条件
    if (activeRecordings.has(anchorId)) {
      log(LOG_LEVEL.INFO, `[onAnchorChange] 主播 ${anchorId} (${anchorName}) 在准备启动时发现已在录制中，跳过`);
      return;
    }
    
    // 文件锁机制已经处理了并发问题，不再需要 cleanupDone
    
    log(LOG_LEVEL.INFO, `[onAnchorChange] 准备通过 anchors 表变化启动主播 ${anchorId} (${anchorName}) 的录制`);
    await appendAnchorLog(anchorId, `准备开始录制任务`);
    
    await startRecording(anchorId, streamUrlData); // 确保 startRecording 是 async 或者能够处理 promise
    await incrementCount(anchorId, 'bcount'); // 记录一次启动尝试
  }
};

/**
 * 初始化程序逻辑
 */
const initializeProgram = async () => {
  // 检测ffmpeg版本
  try {
    const { stdout } = await execPromise('ffmpeg -version');
    const versionMatch = stdout.match(/ffmpeg version ([^\s]+)/);
    if (versionMatch) {
      console.log(`[${getCurrentTime()}] 使用系统 ffmpeg，版本: ${versionMatch[1]}`);
    } else {
      console.log(`[${getCurrentTime()}] 检测到 ffmpeg`);
    }
  } catch (err) {
    console.error(`[${getCurrentTime()}] 无法检测 ffmpeg 版本，请确保已安装并添加到 PATH: ${err.message}`);
    process.exit(1); // ffmpeg是必需的，如果没有则退出
  }

  // 初始化时读取区间
  await updateRangeFromDoes();

  console.log(`[${getCurrentTime()}] 程序启动，开始监听数据库变更`);

  // 初始化时检查一次磁盘空间
  await monitorDiskSpace();

  // 启动时立即检查一次需要录制的任务
  console.log(`[${getCurrentTime()}] 启动时检查需要录制的任务`);
  await checkAndRestartMissedRecordings();

  // 依靠数据库监听机制进行后续的实时监控
  console.log(`[${getCurrentTime()}] 初始化操作完成，监听数据库变更`);

  // 新增：监听 anchors 表的变化
  supabase
    .channel('public-anchors-changes') // 给channel一个唯一的名字
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'anchors'
        // filter: 'status=eq.true' // 如果只想接收 status 变为 true 的事件，可以加filter
        // 但我们需要 oldRecord.status 来判断是否是从 false 变为 true，所以暂时不在这里加 filter
      },
      onAnchorChange
    )
    .subscribe((status, err) => {
      if (err) {
        log(LOG_LEVEL.ERROR, `订阅 anchors 表变化失败: ${err.message || '未知错误'}`);
      }
      log(LOG_LEVEL.INFO, `订阅 anchors 表变化成功，状态: ${status}`);
    });
    
  // 监听 does 表的 scanall 字段变化
  supabase
    .channel('public-does-changes')
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'does'
      },
      async (payload) => {
        // 只显示重要的字段变更，而不是整个payload
        if (payload.new && payload.old) {
          // 检查 scanall 字段是否变化
          if (payload.new.scanall !== payload.old.scanall) {
            console.log(`[${getCurrentTime()}] 监测到 scanall 字段更新: ${payload.old.scanall} -> ${payload.new.scanall}`);
            console.log(`[${getCurrentTime()}] scanall 触发，开始检查当前区间内所有应该录制的主播`);
            
            // 检查磁盘空间
            await monitorDiskSpace();
            
            // 检测并清理僵尸进程
            await detectAndCleanZombieProcesses();
            
            // 检查需要停止的录制：status=false 但 is_recording=true
            await checkAndStopRecordingsWithStatusFalse();
            
            // 执行检查：扫描所有 status=true 但 is_recording=false 的主播并启动录制
            await checkAndRestartMissedRecordings();
          }
          
          // 检查区间是否变化
          if (payload.new[PROGRAM_TAG] !== payload.old[PROGRAM_TAG]) {
            console.log(`[${getCurrentTime()}] ${PROGRAM_TAG} 区间变化: ${payload.old[PROGRAM_TAG]} -> ${payload.new[PROGRAM_TAG]}`);
            
            // 更新区间配置
            await updateRangeFromDoes();
          }
        }
      }
    )
    .subscribe((status, err) => {
      if (err) {
        log(LOG_LEVEL.ERROR, `订阅 does 表变化失败: ${err.message || '未知错误'}`);
      }
      log(LOG_LEVEL.INFO, `订阅 does 表变化成功，状态: ${status}`);
    });
};


// 全局退出处理
process.on('SIGTERM', async () => {
  console.log(`[${getCurrentTime()}] 收到SIGTERM信号，开始优雅关闭...`);
  await gracefulShutdown();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log(`[${getCurrentTime()}] 收到SIGINT信号，开始优雅关闭...`);
  await gracefulShutdown();
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', async (error) => {
  console.error(`[${getCurrentTime()}] 未捕获的异常:`, error);
  await gracefulShutdown();
  process.exit(1);
});

process.on('unhandledRejection', async (reason, promise) => {
  console.error(`[${getCurrentTime()}] 未处理的Promise拒绝:`, reason);
  await gracefulShutdown();
  process.exit(1);
});


/**
 * 生成统一格式的文件名
 * @param {string} anchorId - 主播ID
 * @param {string} type - 文件类型: 'main', 'temp', 'archived'
 * @param {number} timestamp - Unix时间戳（秒）
 * @returns {string} 文件名
 */
function generateFileName(anchorId, type, timestamp = null) {
  const ts = timestamp || Math.floor(Date.now() / 1000);
  
  switch (type) {
    case 'main':
      return `${anchorId}_main.ts`;
    case 'temp':
      return `${ts}-${anchorId}_temp.ts`;
    case 'archived':
      return `${ts}-${anchorId}.ts`;
    default:
      throw new Error(`未知的文件类型: ${type}`);
  }
}

initializeProgram();
